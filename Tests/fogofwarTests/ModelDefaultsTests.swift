import Testing
@testable import fogofwar

@Test func overlayDefaults() async throws {
    let overlay = Overlay()
    #expect(overlay.name == "")
    #expect(overlay.opacity == 1)
}

@Test func trackDefaults() async throws {
    let track = Track()
    #expect(track.colourHex == "#FF0000")
    #expect(track.points.isEmpty)
}

@Test func markerDefaults() async throws {
    let marker = Marker()
    #expect(marker.isVisible)
    #expect(marker.photoAssetID == "")
}

