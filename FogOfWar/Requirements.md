FOG OF WAR — FULL-STACK REQUIREMENTS, UI BLUEPRINT & ROADMAP  
Target OS iOS 18   •   Devices iPhone 16 family (universal iPad support)


0 EXECUTIVE SUMMARY  
Field-mapping utility layering raster overlays, GPS tracks, photo markers
(custom categories) and colour-coded coverage areas.  
Storage = Core Data (visual .xcdatamodeld) with NSPersistentCloudKitContainer
for fully automatic, behind-the-scenes multi-device iCloud sync.  
Rule Every stored attribute is optional *or* has a default value; parents
never embed arrays—child entities own all to-many links.


1 PERSISTENCE & SYNC STACK  
• Model graph defined in *FogOfWar.xcdatamodeld* (single source of truth).  
• Generated NSManagedObject subclasses used in code.  
• Container initialised via NSPersistentCloudKitContainer; viewContext
automaticallyMergesChangesFromParent = true; history tracking on.  
• Store file placed in an App Group URL so Share Extension (future) can access.  
• Private CloudKit container identifier "iCloud.com.yourcompany.fogofwar".  
• Conflict policy = last-write-wins; background push/pull scheduled by
system; manual “Sync Now” button in Settings triggers SchedulePushRequest.


2 DATA MODEL (.xcdatamodeld) – ALL OPTIONAL OR DEFAULTED  

ENTITY Overlay  
  id: UUID (non-optional, Indexed)  
  name: String (default "")  
  overlayDescription: String?  
  imageLocalIdentifier: String?  
  imageURL: URI?  
  centerLatitude: Double (default 0)  
  centerLongitude: Double (default 0)  
  baselineFit: Double (default 0)  
  relativeScale: Double (default 1)  
  rotation: Double (default 0)  
  opacity: Double (default 1)  
  zOrder: Int32 (default 0)  
  isVisible: Boolean (default YES)  
  forwardMatrix: Binary (allow nil)  
  createdAt: Date (default Now)  
  updatedAt: Date (default Now)  
RELATIONSHIPS  
  controlPoints (OverlayControlPoint, to-many, Cascade, inverse overlay)

ENTITY OverlayControlPoint  
  id: UUID (non-optional)  
  pixelX: Double (default 0)  
  pixelY: Double (default 0)  
  latitude: Double (default 0)  
  longitude: Double (default 0)  
  index: Int16 (default 0)  
RELATIONSHIPS  
  overlay (Overlay, to-one, Nullify, inverse controlPoints)

ENTITY Track  
  id: UUID (non-optional)  
  name: String (default "")  
  trackDescription: String?  
  colourHex: String (default "#FF0000")  
  startedAt: Date (default Now)  
  endedAt: Date?  
  lengthMetres: Double (default 0)  
  durationSeconds: Double (default 0)  
  isVisible: Boolean (default YES)  
RELATIONSHIPS  
  points (TrackPoint, to-many, Cascade, inverse track)

ENTITY TrackPoint  
  id: UUID (non-optional)  
  latitude: Double (default 0)  
  longitude: Double (default 0)  
  altitude: Double?  
  horizontalAccuracy: Double (default 0)  
  timestamp: Date (default Now)  
RELATIONSHIPS  
  track (Track, to-one, Nullify, inverse points)

ENTITY MarkerCategory  
  id: UUID (non-optional)  
  name: String (default "")  
  iconName: String (default "mappin")  
  createdAt: Date (default Now)  
RELATIONSHIPS  
  markers (Marker, to-many, Cascade, inverse category)

ENTITY Marker  
  id: UUID (non-optional)  
  name: String (default "")  
  markerDescription: String?  
  iconNameOverride: String?  
  photoAssetID: String (default "")  
  latitude: Double (default 0)  
  longitude: Double (default 0)  
  isVisible: Boolean (default YES)  
  createdAt: Date (default Now)  
RELATIONSHIPS  
  category (MarkerCategory, to-one, Nullify, inverse markers)

ENTITY Area  
  id: UUID (non-optional)  
  name: String (default "")  
  areaDescription: String?  
  colourHex: String (default "#00FF00")  
  opacity: Double (default 0.3)  
  shapeType: String (default "polygon")  
  radiusMetres: Double?  
  isVisible: Boolean (default YES)  
  createdAt: Date (default Now)  
RELATIONSHIPS  
  vertices (AreaVertex, to-many, Cascade, inverse area)

ENTITY AreaVertex  
  id: UUID (non-optional)  
  latitude: Double (default 0)  
  longitude: Double (default 0)  
  index: Int32 (default 0)  
RELATIONSHIPS  
  area (Area, to-one, Nullify, inverse vertices)


3 CORE COMPONENTS (SERVICE / RENDER)  
• Matrix3x3 • OverlayTransform • ImageOverlayRenderer • AreaOverlayRenderer  
• LocationManager (AsyncStream) • TrackThumbnailGenerator  
• MarkerAnnotation (cluster-aware) • ExportCoordinator / ImportCoordinator


4 UI BLUEPRINT (HIGH-FIDELITY LAYOUT)  

APP SHELL  
TabView (iPhone) or NavigationSplitView (iPad)  
Tabs: Map  Overlays  Tracks  Markers  Areas  Settings  
All modals use .sheet(item:) with concrete value types.

MAP TAB (MapScreenView)  
• MKMapViewRepresentable fills screen.  
• Bottom-trailing floating button column  
  location.fill → recenterUser()  
  line.3.horizontal.decrease.circle → layerPanel sheet  
  plus → quickAdd sheet (overlay default)  
• Nav bar trailing: text showing current map scale.

LAYER PANEL (sheet)  
Form with four toggle sections (Overlays, Areas, Tracks, Markers) and a
master opacity slider. Done button in toolbar.

OVERLAY TAB  
NavigationStack list. Row: 44 × 44 thumbnail, title, opacity mini-bar,
eye toggle. Edit mode enables drag-to-reorder (zOrder). Plus button opens
PHPicker sheet. Tapping row pushes OverlayEditView.

OverlayEditView layout  
Map canvas (interactive) top half, Form bottom half: Sliders Opacity •
Rotation (step 15°) • Scale, TextFields Name + Description, Save toolbar
button. Long-press on canvas enters control-point mode.

TRACK TAB  
List rows: colour swatch, name, length, date, eye toggle. Trailing
toolbar button shows record.circle / stop.circle; opens TrackRecording
sheet (Name, ColourPicker) while stopped, or stops recording if active.

MARKER TAB  
On phones < 500 pt width List; on wider screens LazyVGrid thumbnails.
Plus button opens PHPicker sheet. MarkerEditView includes SF Symbol
picker, category picker, map position adjust.

AREA TAB  
List rows: colour patch, name, shape icon, eye toggle. Plus opens
AreaDrawSheet (shape segmented control, colour/opacity pickers, map
gesture canvas). Circle shape stores radiusMetres; polygon/rectangle
store AreaVertex list.

SETTINGS TAB  
Form with MarkerCategory CRUD, CloudKit sync status, “Sync Now” button
(runs SchedulePushRequest), danger-zone Delete All Data.

5 ALGORITHMS & CONTRACTS  
• computeOverlayFrame ≤ 2 px between editor and renderer  
• Track sample rule: distance ≥ 5 m OR elapsed ≥ 1 s AND accuracy ≤ 10 m  
• Marker EXIF auto-place else snap lat/lon to 0.005°  
• Area renderer dispatch by shapeType; circle uses first vertex + radius  
• Energy: GPS logging ≤ 5 %/h on iPhone 16 Pro  
• Cache files protected with NSFileProtection.completeUntilFirstUserAuthentication

6 PHASED IMPLEMENTATION ROADMAP  

PH 0  Core Data stack + .xcdatamodeld entities  
PH 1  UI skeleton & navigation (Tabs, Lists, .sheet(item:))  
PH 2  Map core (MKMapView, MapViewModel, layer toggles)  
PH 3  Overlay feature (import, edit, ImageOverlayRenderer, CRUD)  
PH 4  Track recording (LocationManager, TrackThumbnailGenerator, GPX)  
PH 5  Marker management (categories, import, MarkerAnnotation clustering)  
PH 6  Area drawing & AreaOverlayRenderer  
PH 7  LayerPanelView (visibility + opacity)  
PH 8  Export / Import (.fogofwar, snapshot)  
PH 9  CloudKit sync hardening & diagnostics UI  
PH 10 Polish (accessibility, energy) & TestFlight submission

7 LLM EXECUTION CHECKLIST  

1  Implement .xcdatamodeld schema with defaults/optionals  
2  Generate NSManagedObject subclasses; build Core Data stack with NSPersistentCloudKitContainer  
3  Scaffold AppView with Tabs/Split + Lists + .sheet(item:)  
4  Integrate MapScreenView & MapViewModel  
5  Overlay import, edit, render, list CRUD  
6  Track recording pipeline + GPX export  
7  MarkerCategory CRUD, Marker import & clustering  
8  Area drawing workflow + renderer  
9  LayerPanelView (visibility, opacity)  
10 Export / Import coordinators  
11 CloudKit conflict handling & diagnostics  
12 Unit/UI tests, accessibility, energy profiling, TestFlight build

END OF SPECIFICATION

## Progress Log
- [x] PH 0 Core Data stack + .xcdatamodeld entities
- [x] PH 1 UI skeleton & navigation
- [x] PH 2 Map core
- [x] PH 3 Overlay feature
- [ ] PH 4 Track recording
- [ ] PH 5 Marker management
- [ ] PH 6 Area drawing
- [ ] PH 7 Layer panel
- [ ] PH 8 Export / Import
- [ ] PH 9 CloudKit sync hardening
- [ ] PH 10 Polish & TestFlight
