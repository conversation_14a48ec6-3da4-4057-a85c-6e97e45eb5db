<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<model type="com.apple.IDECoreDataModeler.DataModel" documentVersion="1.0" lastSavedToolsVersion="1" systemVersion="11A491" minimumToolsVersion="Automatic" sourceLanguage="Swift" usedWithCloudKit="true" userDefinedModelVersionIdentifier="">
    <entity name="Overlay" representedClassName="Overlay" syncable="YES" codeGenerationType="class">
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO" optional="YES"/>
        <attribute name="name" attributeType="String" optional="NO" defaultValueString=""/>
        <attribute name="overlayDescription" attributeType="String" optional="YES"/>
        <attribute name="imageLocalIdentifier" attributeType="String" optional="YES"/>
        <attribute name="imageURL" attributeType="URI" optional="YES"/>
        <attribute name="centerLatitude" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="centerLongitude" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="baselineFit" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="relativeScale" attributeType="Double" optional="NO" defaultValueString="1"/>
        <attribute name="rotation" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="opacity" attributeType="Double" optional="NO" defaultValueString="1"/>
        <attribute name="zOrder" attributeType="Integer 32" optional="NO" defaultValueString="0"/>
        <attribute name="isVisible" attributeType="Boolean" optional="NO" defaultValueString="YES"/>
        <attribute name="forwardMatrix" attributeType="Binary" optional="YES"/>
        <attribute name="createdAt" attributeType="Date" optional="YES"/>
        <attribute name="updatedAt" attributeType="Date" optional="YES"/>
        <relationship name="controlPoints" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="OverlayControlPoint" inverseName="overlay" inverseEntity="OverlayControlPoint"/>
    </entity>
    <entity name="OverlayControlPoint" representedClassName="OverlayControlPoint" syncable="YES" codeGenerationType="class">
        <attribute name="pixelX" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="pixelY" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="latitude" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="longitude" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="index" attributeType="Integer 16" optional="NO" defaultValueString="0"/>
        <relationship name="overlay" optional="YES" toMany="NO" deletionRule="Nullify" destinationEntity="Overlay" inverseName="controlPoints" inverseEntity="Overlay"/>
    </entity>
    <entity name="Track" representedClassName="Track" syncable="YES" codeGenerationType="class">
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO" optional="YES"/>
        <attribute name="name" attributeType="String" optional="NO" defaultValueString=""/>
        <attribute name="trackDescription" attributeType="String" optional="YES"/>
        <attribute name="colourHex" attributeType="String" optional="NO" defaultValueString="#FF0000"/>
        <attribute name="startedAt" attributeType="Date" optional="YES"/>
        <attribute name="endedAt" attributeType="Date" optional="YES"/>
        <attribute name="lengthMetres" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="durationSeconds" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="isVisible" attributeType="Boolean" optional="NO" defaultValueString="YES"/>
        <relationship name="points" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="TrackPoint" inverseName="track" inverseEntity="TrackPoint"/>
    </entity>
    <entity name="TrackPoint" representedClassName="TrackPoint" syncable="YES" codeGenerationType="class">
        <attribute name="latitude" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="longitude" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="altitude" attributeType="Double" optional="YES"/>
        <attribute name="horizontalAccuracy" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="timestamp" attributeType="Date" optional="YES"/>
        <relationship name="track" optional="YES" toMany="NO" deletionRule="Nullify" destinationEntity="Track" inverseName="points" inverseEntity="Track"/>
    </entity>
    <entity name="MarkerCategory" representedClassName="MarkerCategory" syncable="YES" codeGenerationType="class">
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO" optional="YES"/>
        <attribute name="name" attributeType="String" optional="NO" defaultValueString=""/>
        <attribute name="iconName" attributeType="String" optional="NO" defaultValueString="mappin"/>
        <attribute name="createdAt" attributeType="Date" optional="YES"/>
        <relationship name="markers" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="Marker" inverseName="category" inverseEntity="Marker"/>
    </entity>
    <entity name="Marker" representedClassName="Marker" syncable="YES" codeGenerationType="class">
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO" optional="YES"/>
        <attribute name="name" attributeType="String" optional="NO" defaultValueString=""/>
        <attribute name="markerDescription" attributeType="String" optional="YES"/>
        <attribute name="iconNameOverride" attributeType="String" optional="YES"/>
        <attribute name="photoAssetID" attributeType="String" optional="NO" defaultValueString=""/>
        <attribute name="latitude" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="longitude" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="isVisible" attributeType="Boolean" optional="NO" defaultValueString="YES"/>
        <attribute name="createdAt" attributeType="Date" optional="YES"/>
        <relationship name="category" optional="YES" toMany="NO" deletionRule="Nullify" destinationEntity="MarkerCategory" inverseName="markers" inverseEntity="MarkerCategory"/>
    </entity>
    <entity name="Area" representedClassName="Area" syncable="YES" codeGenerationType="class">
        <attribute name="id" attributeType="UUID" usesScalarValueType="NO" optional="YES"/>
        <attribute name="name" attributeType="String" optional="NO" defaultValueString=""/>
        <attribute name="areaDescription" attributeType="String" optional="YES"/>
        <attribute name="colourHex" attributeType="String" optional="NO" defaultValueString="#00FF00"/>
        <attribute name="opacity" attributeType="Double" optional="NO" defaultValueString="0.3"/>
        <attribute name="shapeType" attributeType="String" optional="NO" defaultValueString="polygon"/>
        <attribute name="radiusMetres" attributeType="Double" optional="YES"/>
        <attribute name="isVisible" attributeType="Boolean" optional="NO" defaultValueString="YES"/>
        <attribute name="createdAt" attributeType="Date" optional="YES"/>
        <relationship name="vertices" optional="YES" toMany="YES" deletionRule="Cascade" destinationEntity="AreaVertex" inverseName="area" inverseEntity="AreaVertex"/>
    </entity>
    <entity name="AreaVertex" representedClassName="AreaVertex" syncable="YES" codeGenerationType="class">
        <attribute name="latitude" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="longitude" attributeType="Double" optional="NO" defaultValueString="0"/>
        <attribute name="index" attributeType="Integer 32" optional="NO" defaultValueString="0"/>
        <relationship name="area" optional="YES" toMany="NO" deletionRule="Nullify" destinationEntity="Area" inverseName="vertices" inverseEntity="Area"/>
    </entity>
    <elements>
        <element name="Overlay" positionX="0" positionY="0" width="128" height="44"/>
        <element name="OverlayControlPoint" positionX="200" positionY="0" width="128" height="44"/>
        <element name="Track" positionX="0" positionY="80" width="128" height="44"/>
        <element name="TrackPoint" positionX="200" positionY="80" width="128" height="44"/>
        <element name="MarkerCategory" positionX="0" positionY="160" width="128" height="44"/>
        <element name="Marker" positionX="200" positionY="160" width="128" height="44"/>
        <element name="Area" positionX="0" positionY="240" width="128" height="44"/>
        <element name="AreaVertex" positionX="200" positionY="240" width="128" height="44"/>
    </elements>
</model>
