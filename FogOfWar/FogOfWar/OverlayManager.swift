import SwiftUI
import PhotosUI

/// Sheet used when importing a new overlay image.
/// The user first selects an image and is then presented with `OverlayEditView`
/// to position and configure the overlay before saving.
struct OverlayManager: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    var viewModel: MapViewModel

    @State private var selectedItem: PhotosPickerItem?
    @State private var overlay: Overlay?
    @State private var showingPicker = true

    var body: some View {
        NavigationStack {
            Group {
                if let overlay {
                    OverlayEditView(overlay: overlay, viewModel: viewModel)
                } else {
                    Color.clear
                        .photosPicker(isPresented: $showingPicker,
                                      selection: $selectedItem,
                                      matching: .images)
                        .onChange(of: selectedItem) { _ in
                            importSelectedImage()
                        }
                        .onAppear { showingPicker = true }
                        .navigationTitle("New Overlay")
                }
            }
        }
    }

    /// Creates a temporary Overlay object from the chosen image.
    private func importSelectedImage() {
        guard let item = selectedItem else { return }
        Task {
            if let data = try? await item.loadTransferable(type: Data.self) {
                let url = FileManager.default.temporaryDirectory
                    .appendingPathComponent(UUID().uuidString + ".jpg")
                try? data.write(to: url)

                let newOverlay = Overlay(context: viewContext)
                newOverlay.id = UUID()
                newOverlay.name = "Imported Overlay"
                newOverlay.createdAt = Date()
                newOverlay.updatedAt = Date()

                if let center = viewModel.centerCoordinate {
                    newOverlay.centerLatitude = center.latitude
                    newOverlay.centerLongitude = center.longitude
                }

                newOverlay.baselineFit = 1
                if let mapWidthPts = viewModel.mapWidthPoints {
                    newOverlay.relativeScale = mapWidthPts * 0.45
                } else {
                    newOverlay.relativeScale = 1000
                }
                newOverlay.rotation = 0
                newOverlay.opacity = 0.5
                newOverlay.zOrder = 0
                newOverlay.isVisible = true
                newOverlay.imageURL = url
                overlay = newOverlay
            }
        }
    }
}

