import Foundation
import SwiftUI
import PhotosUI

struct OverlayManager: View {
    @Environment(\.managedObjectContext) private var viewContext
    @State private var showingPicker = false
    @State private var selectedItem: PhotosPickerItem?

    var body: some View {
        Button("Add Overlay") {
            showingPicker = true
        }
        .photosPicker(isPresented: $showingPicker, selection: $selectedItem)
        .onChange(of: selectedItem) { _ in
            importSelectedImage()
        }
    }

    private func importSelectedImage() {
        guard let item = selectedItem else { return }
        Task {
            if let data = try? await item.loadTransferable(type: Data.self) {
                let url = FileManager.default.temporaryDirectory.appendingPathComponent(UUID().uuidString + ".jpg")
                try? data.write(to: url)
                let overlay = Overlay(context: viewContext)
                overlay.id = UUID()
                overlay.name = "Imported Overlay"
                overlay.createdAt = Date()
                overlay.updatedAt = Date()
                overlay.centerLatitude = 0
                overlay.centerLongitude = 0
                overlay.baselineFit = 1
                overlay.relativeScale = 1000
                overlay.rotation = 0
                overlay.opacity = 0.5
                overlay.zOrder = 0
                overlay.isVisible = true
                overlay.imageURL = url
                do {
                    try viewContext.save()
                } catch {
                    print("Failed to save overlay: \(error)")
                }
            }
        }
    }
}
