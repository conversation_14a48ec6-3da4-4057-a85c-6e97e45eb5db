import SwiftUI
import PhotosUI

/// Sheet used when importing a new overlay image.
/// The user first selects an image and is then presented with `OverlayEditView`
/// to position and configure the overlay before saving.
struct OverlayManager: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    var viewModel: MapViewModel

    @State private var selectedItem: PhotosPickerItem?
    @State private var overlay: Overlay?
    @State private var showingPicker = true

    var body: some View {
        NavigationStack {
            Group {
                if let overlay {
                    OverlayEditView(overlay: overlay, viewModel: viewModel)
                } else {
                    Color.clear
                        .photosPicker(isPresented: $showingPicker,
                                      selection: $selectedItem,
                                      matching: .images)
                        .onChange(of: selectedItem) { _ in
                            importSelectedImage()
                        }
                        .onAppear { showingPicker = true }
                        .navigationTitle("New Overlay")
                }
            }
        }
    }

    /// Creates a temporary Overlay object from the chosen image.
    private func importSelectedImage() {
        guard let item = selectedItem else { return }
        Task {
            if let data = try? await item.loadTransferable(type: Data.self) {
                // Store in Documents directory instead of temp directory
                let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
                let overlaysPath = documentsPath.appendingPathComponent("Overlays", isDirectory: true)

                // Create overlays directory if it doesn't exist
                try? FileManager.default.createDirectory(at: overlaysPath, withIntermediateDirectories: true)

                let filename = UUID().uuidString + ".jpg"
                let url = overlaysPath.appendingPathComponent(filename)
                try? data.write(to: url)

                print("Saved overlay image to: \(url.path)")

                let newOverlay = Overlay(context: viewContext)
                newOverlay.id = UUID()
                newOverlay.name = "Imported Overlay"
                newOverlay.createdAt = Date()
                newOverlay.updatedAt = Date()

                // Use current map center, or default to the specified location
                if let center = viewModel.centerCoordinate,
                   center.latitude != 0.0 && center.longitude != 0.0 {
                    newOverlay.centerLatitude = center.latitude
                    newOverlay.centerLongitude = center.longitude
                } else {
                    // Default to the specified location (47.073692, -80.103519)
                    newOverlay.centerLatitude = 47.073692
                    newOverlay.centerLongitude = -80.103519
                }

                newOverlay.baselineFit = 1
                // Use a much smaller, more reasonable scale (1km instead of thousands of km)
                if let mapWidthMeters = viewModel.mapWidthMeters {
                    newOverlay.relativeScale = min(mapWidthMeters * 0.1, 1000) // 10% of visible area, max 1km
                } else {
                    newOverlay.relativeScale = 1000 // 1km default
                }
                newOverlay.rotation = 0
                newOverlay.opacity = 0.5
                newOverlay.zOrder = 0
                newOverlay.isVisible = true
                newOverlay.imageURL = url
                overlay = newOverlay

                print("Created overlay at \(newOverlay.centerLatitude), \(newOverlay.centerLongitude) with scale \(newOverlay.relativeScale)m")
            }
        }
    }
}

