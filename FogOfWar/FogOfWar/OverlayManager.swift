import SwiftUI
import PhotosUI

/// Sheet used when importing a new overlay image.
/// The user first selects an image and is then presented with `OverlayEditView`
/// to position and configure the overlay before saving.
struct OverlayManager: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    var viewModel: MapViewModel

    @State private var selectedItem: PhotosPickerItem?
    @State private var overlay: Overlay?
    @State private var showingPicker = true

    var body: some View {
        NavigationStack {
            Group {
                if let overlay {
                    OverlayEditView(overlay: overlay, viewModel: viewModel)
                } else {
                    Color.clear
                        .photosPicker(isPresented: $showingPicker,
                                      selection: $selectedItem,
                                      matching: .images)
                        .onChange(of: selectedItem) { _ in
                            importSelectedImage()
                        }
                        .onAppear { showingPicker = true }
                        .navigationTitle("New Overlay")
                }
            }
        }
    }

    /// Creates a temporary Overlay object from the chosen image.
    private func importSelectedImage() {
        guard let item = selectedItem else { return }
        Task {
            if let data = try? await item.loadTransferable(type: Data.self),
               let image = UIImage(data: data) {

                // Use ImageManager to save the image properly
                let overlayID = UUID()
                guard let imageURL = ImageManager.shared.saveImage(image, withID: overlayID) else {
                    print("Failed to save image using ImageManager")
                    return
                }

                print("Saved overlay image to: \(imageURL.path)")

                let newOverlay = Overlay(context: viewContext)
                newOverlay.id = overlayID
                newOverlay.name = "Imported Overlay"
                newOverlay.createdAt = Date()
                newOverlay.updatedAt = Date()

                // Use current map center, or default to the specified location
                if let center = viewModel.centerCoordinate,
                   center.latitude != 0.0 && center.longitude != 0.0 {
                    newOverlay.centerLatitude = center.latitude
                    newOverlay.centerLongitude = center.longitude
                } else {
                    // Default to the specified location (47.073692, -80.103519)
                    newOverlay.centerLatitude = 47.073692
                    newOverlay.centerLongitude = -80.103519
                }

                newOverlay.baselineFit = 1
                // Use a reasonable scale based on image size and map view
                let imageSize = image.size
                let maxDimension = max(imageSize.width, imageSize.height)

                // Calculate a reasonable scale based on image size
                // Larger images get larger default scales, but cap it at 2km
                let baseScale = min(Double(maxDimension) * 0.5, 2000) // 0.5m per pixel, max 2km
                newOverlay.relativeScale = max(baseScale, 100) // Minimum 100m

                newOverlay.rotation = 0
                newOverlay.opacity = 0.7
                newOverlay.zOrder = 0
                newOverlay.isVisible = true
                newOverlay.imageURL = imageURL
                overlay = newOverlay

                print("Created overlay at \(newOverlay.centerLatitude), \(newOverlay.centerLongitude) with scale \(newOverlay.relativeScale)m")
            }
        }
    }
}

