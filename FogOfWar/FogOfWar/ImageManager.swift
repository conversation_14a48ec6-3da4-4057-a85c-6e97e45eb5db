import UIKit
import CoreData
import SwiftUI

/// Manages image storage and retrieval for overlays
class ImageManager {
    static let shared = ImageManager()

    private let fileManager = FileManager.default

    // Image cache to avoid reloading images from disk
    private let imageCache = NSCache<NSString, UIImage>()

    // Maximum image dimension for downsampling (4096px as per requirements)
    private let maxImageDimension: CGFloat = 4096

    // Default placeholder image
    private lazy var placeholderImage: UIImage = {
        // Create a simple placeholder with text
        let size = CGSize(width: 512, height: 512)
        UIGraphicsBeginImageContextWithOptions(size, false, 1.0)

        // Fill with light gray
        UIColor.lightGray.setFill()
        UIRectFill(CGRect(origin: .zero, size: size))

        // Add text
        let text = "Image Not Found"
        let attributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.boldSystemFont(ofSize: 32),
            .foregroundColor: UIColor.white
        ]
        let textSize = text.size(withAttributes: attributes)
        let rect = CGRect(
            x: (size.width - textSize.width) / 2,
            y: (size.height - textSize.height) / 2,
            width: textSize.width,
            height: textSize.height
        )
        text.draw(in: rect, withAttributes: attributes)

        let image = UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
        UIGraphicsEndImageContext()

        return image
    }()

    /// Directory where all overlay images are stored
    private var imagesDirectory: URL {
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let imagesDirectory = documentsDirectory.appendingPathComponent("OverlayImages", isDirectory: true)

        // Create directory if it doesn't exist
        if !fileManager.fileExists(atPath: imagesDirectory.path) {
            try? fileManager.createDirectory(at: imagesDirectory, withIntermediateDirectories: true)
        }

        return imagesDirectory
    }

    /// Save an image and return its URL
    func saveImage(_ image: UIImage, withID id: UUID? = nil) -> URL? {
        // Generate a unique ID if none provided
        let imageID = id ?? UUID()

        // Create a filename with the ID
        let filename = "\(imageID.uuidString).jpg"
        let fileURL = imagesDirectory.appendingPathComponent(filename)

        // Ensure the image is in .up orientation and downsample if needed
        let processedImage = processImageForStorage(image)

        // Save the image as JPEG
        if let imageData = processedImage.jpegData(compressionQuality: 0.9) {
            do {
                try imageData.write(to: fileURL)
                print("ImageManager: Successfully saved image to \(fileURL.path)")

                // Add to cache
                imageCache.setObject(processedImage, forKey: fileURL.path as NSString)

                return fileURL
            } catch {
                print("ImageManager: Error saving image: \(error)")
                return nil
            }
        }

        return nil
    }

    /// Process an image for storage - correct orientation and downsample if needed
    private func processImageForStorage(_ image: UIImage) -> UIImage {
        // Step 1: Correct orientation to .up
        let correctedImage: UIImage
        if image.imageOrientation != .up {
            UIGraphicsBeginImageContextWithOptions(image.size, false, image.scale)
            image.draw(in: CGRect(origin: .zero, size: image.size))
            correctedImage = UIGraphicsGetImageFromCurrentImageContext() ?? image
            UIGraphicsEndImageContext()
        } else {
            correctedImage = image
        }

        // Step 2: Downsample if needed
        let maxDimension = max(correctedImage.size.width, correctedImage.size.height)
        if maxDimension > maxImageDimension {
            let scale = maxImageDimension / maxDimension
            let newSize = CGSize(
                width: correctedImage.size.width * scale,
                height: correctedImage.size.height * scale
            )

            UIGraphicsBeginImageContextWithOptions(newSize, false, 1.0)
            correctedImage.draw(in: CGRect(origin: .zero, size: newSize))
            let downsampledImage = UIGraphicsGetImageFromCurrentImageContext() ?? correctedImage
            UIGraphicsEndImageContext()

            print("ImageManager: Downsampled image from \(correctedImage.size) to \(downsampledImage.size)")
            return downsampledImage
        }

        return correctedImage
    }

    /// Load an image from a URL
    func loadImage(from url: URL?) -> UIImage? {
        guard let url = url else {
            print("ImageManager: No URL provided")
            return placeholderImage
        }

        // Check cache first
        if let cachedImage = imageCache.object(forKey: url.path as NSString) {
            print("ImageManager: Retrieved image from cache for \(url.path)")
            return cachedImage
        }

        // First try the exact URL
        if fileManager.fileExists(atPath: url.path) {
            do {
                let imageData = try Data(contentsOf: url)
                if let image = UIImage(data: imageData) {
                    print("ImageManager: Successfully loaded image from \(url.path)")
                    // Add to cache
                    imageCache.setObject(image, forKey: url.path as NSString)
                    return image
                }
            } catch {
                print("ImageManager: Error loading image from \(url.path): \(error)")
            }
        }

        // If that fails, try to find the file by name in our images directory
        let filename = url.lastPathComponent
        let localURL = imagesDirectory.appendingPathComponent(filename)

        if fileManager.fileExists(atPath: localURL.path) {
            do {
                let imageData = try Data(contentsOf: localURL)
                if let image = UIImage(data: imageData) {
                    print("ImageManager: Successfully loaded image from alternative path \(localURL.path)")
                    // Add to cache
                    imageCache.setObject(image, forKey: url.path as NSString)
                    imageCache.setObject(image, forKey: localURL.path as NSString)
                    return image
                }
            } catch {
                print("ImageManager: Error loading image from alternative path: \(error)")
            }
        }

        print("ImageManager: Failed to find image at any location for \(url.path)")
        return placeholderImage
    }

    /// Get a placeholder image when the real image can't be loaded
    func getPlaceholderImage() -> UIImage {
        return placeholderImage
    }

    /// Update an overlay's image URL to point to the correct location
    func updateOverlayImageURL(_ overlay: Overlay) {
        guard let currentURL = overlay.imageURL else {
            print("ImageManager: Overlay has no image URL")
            return
        }

        // If the URL is already in our images directory and the file exists, no need to update
        if currentURL.path.contains("/OverlayImages/") && fileManager.fileExists(atPath: currentURL.path) {
            print("ImageManager: Overlay URL is already correct at \(currentURL.path)")
            return
        }

        // Try to find the image and update the URL
        let filename = currentURL.lastPathComponent
        let correctURL = imagesDirectory.appendingPathComponent(filename)

        if fileManager.fileExists(atPath: correctURL.path) {
            // Update the URL in the overlay
            overlay.imageURL = correctURL
            do {
                try overlay.managedObjectContext?.save()
                print("ImageManager: Updated overlay URL to \(correctURL.path)")
            } catch {
                print("ImageManager: Error saving context after updating URL: \(error)")
            }
        }
    }
}
