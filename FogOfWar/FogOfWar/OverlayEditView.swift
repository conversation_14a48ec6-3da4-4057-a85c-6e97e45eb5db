import SwiftUI
import CoreData

struct OverlayEditView: View {
    @ObservedObject var overlay: Overlay
    var viewModel: MapViewModel
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        ZStack(alignment: .bottom) {
            MapViewRepresentable(viewModel: viewModel, overlays: [overlay])
                .ignoresSafeArea()
            VStack(spacing: 8) {
                TextField("Name", text: Binding(
                    get: { overlay.name ?? "" },
                    set: { overlay.name = $0 }
                ))
                .textFieldStyle(.roundedBorder)
                TextField("Description", text: Binding(
                    get: { overlay.overlayDescription ?? "" },
                    set: { overlay.overlayDescription = $0 }
                ))
                .textFieldStyle(.roundedBorder)
                HStack {
                    Text("Opacity")
                    Slider(value: Binding(
                        get: { overlay.opacity },
                        set: { overlay.opacity = $0 }
                    ), in: 0...1)
                }
                HStack {
                    Text("Rotation")
                    Slider(value: Binding(
                        get: { overlay.rotation },
                        set: { overlay.rotation = $0 }
                    ), in: 0...(Double.pi * 2))
                }
                HStack {
                    Text("Scale")
                    Slider(value: Binding(
                        get: { overlay.relativeScale },
                        set: { overlay.relativeScale = $0 }
                    ), in: 100...10000)
                }
            }
            .padding()
            .background(.regularMaterial)
            .frame(maxWidth: .infinity)
        }
        .navigationTitle("Edit Overlay")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    overlay.updatedAt = Date()
                    do { try viewContext.save() } catch { print("Save error: \(error)") }
                    dismiss()
                }
            }
        }
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let overlay = Overlay(context: context)
    overlay.name = "Sample"
    overlay.imageURL = URL(fileURLWithPath: "/dev/null")
    return NavigationStack { OverlayEditView(overlay: overlay, viewModel: MapViewModel()) }
        .environment(\.managedObjectContext, context)
}
