import SwiftUI
import CoreData

struct OverlayEditView: View {
    @ObservedObject var overlay: Overlay
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        VStack {
            MapViewRepresentable(viewModel: MapViewModel(), overlays: [overlay])
                .frame(height: 200)
                .padding(.bottom)
            Form {
                TextField("Name", text: Binding(
                    get: { overlay.name ?? "" },
                    set: { overlay.name = $0 }
                ))
                Slider(value: Binding(
                    get: { overlay.opacity },
                    set: { overlay.opacity = $0 }
                ), in: 0...1) {
                    Text("Opacity")
                }
                Slider(value: Binding(
                    get: { overlay.rotation },
                    set: { overlay.rotation = $0 }
                ), in: 0..<(Double.pi * 2)) {
                    Text("Rotation")
                }
                Slider(value: Binding(
                    get: { overlay.relativeScale },
                    set: { overlay.relativeScale = $0 }
                ), in: 100...10000) {
                    Text("Scale")
                }
            }
        }
        .navigationTitle("Edit Overlay")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    overlay.updatedAt = Date()
                    do { try viewContext.save() } catch { print("Save error: \(error)") }
                    dismiss()
                }
            }
        }
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let overlay = Overlay(context: context)
    overlay.name = "Sample"
    overlay.imageURL = URL(fileURLWithPath: "/dev/null")
    return NavigationStack { OverlayEditView(overlay: overlay) }
        .environment(\.managedObjectContext, context)
}
