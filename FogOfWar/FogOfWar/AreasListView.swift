import SwiftUI
import CoreData

struct AreasListView: View {
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Area.createdAt, ascending: true)],
        animation: .default)
    private var areas: FetchedResults<Area>

    var body: some View {
        List(areas) { area in
            Text(area.name ?? "Unnamed")
        }
        .navigationTitle("Areas")
    }
}

#Preview {
    AreasListView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
