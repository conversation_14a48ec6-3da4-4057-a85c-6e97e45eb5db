import SwiftUI
import CoreData

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext

    var body: some View {
        TabView {
            NavigationStack {
                MapViewContent()
                    .ignoresSafeArea()
            }
            .tabItem {
                Label("Map", systemImage: "map")
            }

            NavigationStack {
                OverlayListView()
            }
            .tabItem {
                Label("Overlays", systemImage: "photo.on.rectangle")
            }

            NavigationStack {
                TracksListView()
            }
            .tabItem {
                Label("Tracks", systemImage: "waveform.path")
            }

            NavigationStack {
                MarkersListView()
            }
            .tabItem {
                Label("Markers", systemImage: "mappin")
            }

            NavigationStack {
                AreasListView()
            }
            .tabItem {
                Label("Areas", systemImage: "square.dashed")
            }

            NavigationStack {
                SettingsView()
            }
            .tabItem {
                Label("Settings", systemImage: "gear")
            }
        }
    }
}

#Preview {
    ContentView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
