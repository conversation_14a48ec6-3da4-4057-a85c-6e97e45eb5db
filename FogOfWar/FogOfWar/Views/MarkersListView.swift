import SwiftUI
import CoreData

struct MarkersListView: View {
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Marker.createdAt, ascending: true)],
        animation: .default)
    private var markers: FetchedResults<Marker>

    var body: some View {
        List(markers) { marker in
            Text(marker.name ?? "Unnamed")
        }
        .navigationTitle("Markers")
    }
}

#Preview {
    MarkersListView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
