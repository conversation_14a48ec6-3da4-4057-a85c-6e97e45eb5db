import SwiftUI
import CoreData

struct TracksListView: View {
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Track.startedAt, ascending: false)],
        animation: .default)
    private var tracks: FetchedResults<Track>

    var body: some View {
        List(tracks) { track in
            Text(track.name ?? "Unnamed")
        }
        .navigationTitle("Tracks")
    }
}

#Preview {
    TracksListView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
