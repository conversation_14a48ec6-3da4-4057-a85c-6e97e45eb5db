import SwiftUI
import CoreData

struct OverlayListView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Overlay.createdAt, ascending: true)],
        animation: .default)
    private var overlays: FetchedResults<Overlay>

    @State private var showingImporter = false

    var body: some View {
        List {
            ForEach(overlays) { overlay in
                NavigationLink(destination: OverlayEditView(overlay: overlay)) {
                    HStack {
                        Text(overlay.name ?? "Unnamed")
                        Spacer()
                        Toggle("", isOn: Binding(
                            get: { overlay.isVisible },
                            set: { overlay.isVisible = $0; save() }
                        ))
                        .labelsHidden()
                    }
                }
            }
            .onDelete(perform: delete)
        }
        .navigationTitle("Overlays")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: { showingImporter = true }) {
                    Image(systemName: "plus")
                }
            }
        }
        .sheet(isPresented: $showingImporter) {
            OverlayManager()
        }
    }

    private func delete(at offsets: IndexSet) {
        offsets.map { overlays[$0] }.forEach(viewContext.delete)
        save()
    }

    private func save() {
        do {
            try viewContext.save()
        } catch {
            print("Save error: \(error)")
        }
    }
}
