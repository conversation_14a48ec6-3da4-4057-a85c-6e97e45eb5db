import SwiftUI
import MapKit

@MainActor
final class MapViewModel: NSObject, ObservableObject, MKMapViewDelegate {
    @Published var showOverlays = true
    @Published var showTracks = true
    @Published var showMarkers = true
    @Published var showAreas = true
    @Published var scaleText: String = ""

    private weak var mapView: MKMapView?
    private var lastRegion: MKCoordinateRegion?

    /// Current center coordinate of the bound map view, if available.
    var centerCoordinate: CLLocationCoordinate2D? {
        mapView?.centerCoordinate
    }

    /// Approximate width of the current map view in meters.
    var mapWidthMeters: Double? {
        guard let mapView else { return nil }
        let metersPerPoint = MKMetersPerMapPointAtLatitude(mapView.region.center.latitude)
        return metersPerPoint * Double(mapView.bounds.width)
    }

    /// Width of the current map view in map points.
    var mapWidthPoints: Double? {
        mapView?.visibleMapRect.size.width
    }

    /// Current map region of the view model if available.
    var currentRegion: MKCoordinateRegion? {
        mapView?.region ?? lastRegion
    }

    func setMapView(_ mapView: MKMapView) {
        self.mapView = mapView
        mapView.delegate = self
        if let region = lastRegion {
            mapView.setRegion(region, animated: false)
        }
    }

    func recenterUser() {
        mapView?.setUserTrackingMode(.follow, animated: true)
    }

    func mapViewDidChangeVisibleRegion(_ mapView: MKMapView) {
        lastRegion = mapView.region
        updateScaleText(for: mapView)
    }

    func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
        print("MapView delegate called for overlay renderer")
        if let imageOverlay = overlay as? ImageOverlay {
            print("Creating ImageOverlayRenderer for overlay: \(imageOverlay.overlayData.name ?? "Unnamed")")
            return ImageOverlayRenderer(overlay: imageOverlay)
        }
        print("Creating default MKOverlayRenderer")
        return MKOverlayRenderer(overlay: overlay)
    }

    private func updateScaleText(for mapView: MKMapView) {
        let region = mapView.region
        let metersPerPoint = MKMetersPerMapPointAtLatitude(region.center.latitude)
        let mapWidth = metersPerPoint * Double(mapView.bounds.width)
        if mapWidth >= 1000 {
            scaleText = String(format: "%.1f km", mapWidth/1000)
        } else {
            scaleText = String(format: "%.0f m", mapWidth)
        }
    }
}
