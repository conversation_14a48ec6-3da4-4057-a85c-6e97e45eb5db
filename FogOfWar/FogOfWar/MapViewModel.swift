import SwiftUI
import MapKit

@MainActor
final class MapViewModel: NSObject, ObservableObject, MKMapViewDelegate {
    @Published var showOverlays = true
    @Published var showTracks = true
    @Published var showMarkers = true
    @Published var showAreas = true
    @Published var scaleText: String = ""

    private weak var mapView: MKMapView?

    func setMapView(_ mapView: MKMapView) {
        self.mapView = mapView
        mapView.delegate = self
    }

    func recenterUser() {
        mapView?.setUserTrackingMode(.follow, animated: true)
    }

    func mapViewDidChangeVisibleRegion(_ mapView: MKMapView) {
        updateScaleText(for: mapView)
    }

    func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
        if let imageOverlay = overlay as? ImageOverlay {
            return ImageOverlayRenderer(overlay: imageOverlay)
        }
        return MKOverlayRenderer(overlay: overlay)
    }

    private func updateScaleText(for mapView: MKMapView) {
        let region = mapView.region
        let metersPerPoint = MKMetersPerMapPointAtLatitude(region.center.latitude)
        let mapWidth = metersPerPoint * Double(mapView.bounds.width)
        if mapWidth >= 1000 {
            scaleText = String(format: "%.1f km", mapWidth/1000)
        } else {
            scaleText = String(format: "%.0f m", mapWidth)
        }
    }
}
