import MapKit
import CoreGraphics
import simd

enum TransformError: Error {
    case insufficientControlPoints
    case singularMatrix
    case invalidInput
}

class OverlayTransform {
    static func computeOverlayFrame(overlay: Overlay, in mapView: MKMapView) -> CGRect {
        // Load the image to get its size
        var imageSize = CGSize(width: 1000, height: 1000) // Default size

        // Use ImageManager to load the image and get its size
        if let image = ImageManager.shared.loadImage(from: overlay.imageURL) {
            imageSize = image.size

            // Make sure the overlay has the correct URL
            ImageManager.shared.updateOverlayImageURL(overlay)
        }

        // Create a basic frame based on center, scale, and rotation
        let center = mapView.convert(
            CLLocationCoordinate2D(latitude: overlay.centerLatitude, longitude: overlay.centerLongitude),
            toPointTo: mapView
        )

        // Apply rotation and scale to all four corners
        let topLeft = CGPoint(x: -imageSize.width/2, y: -imageSize.height/2)
        let topRight = CGPoint(x: imageSize.width/2, y: -imageSize.height/2)
        let bottomLeft = CGPoint(x: -imageSize.width/2, y: imageSize.height/2)
        let bottomRight = CGPoint(x: imageSize.width/2, y: imageSize.height/2)

        // Create a transform
        var transform = CGAffineTransform.identity
        transform = transform.translatedBy(x: center.x, y: center.y)
        transform = transform.rotated(by: CGFloat(overlay.rotation))
        transform = transform.scaledBy(x: CGFloat(overlay.relativeScale), y: CGFloat(overlay.relativeScale))

        // Apply transform to all corners
        let transformedTopLeft = topLeft.applying(transform)
        let transformedTopRight = topRight.applying(transform)
        let transformedBottomLeft = bottomLeft.applying(transform)
        let transformedBottomRight = bottomRight.applying(transform)

        // Find the bounding rect
        let minX = min(transformedTopLeft.x, transformedTopRight.x, transformedBottomLeft.x, transformedBottomRight.x)
        let minY = min(transformedTopLeft.y, transformedTopRight.y, transformedBottomLeft.y, transformedBottomRight.y)
        let maxX = max(transformedTopLeft.x, transformedTopRight.x, transformedBottomLeft.x, transformedBottomRight.x)
        let maxY = max(transformedTopLeft.y, transformedTopRight.y, transformedBottomLeft.y, transformedBottomRight.y)

        return CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
    }

    // For future use - transform matrix support
    static func computeOverlayFrameWithMatrix(overlay: Overlay, in mapView: MKMapView) -> CGRect {
        // Load the image to get its size
        var imageSize = CGSize(width: 1000, height: 1000) // Default size

        if let image = ImageManager.shared.loadImage(from: overlay.imageURL) {
            imageSize = image.size
        }

        // If we have a transform matrix, use it
        if let transformData = overlay.forwardMatrix,
           let matrix = try? JSONDecoder().decode(Matrix3x3.self, from: transformData) {

            // Apply the matrix to all four corners of the image
            let topLeft = CGPoint(x: 0, y: 0)
            let topRight = CGPoint(x: imageSize.width, y: 0)
            let bottomLeft = CGPoint(x: 0, y: imageSize.height)
            let bottomRight = CGPoint(x: imageSize.width, y: imageSize.height)

            // Transform all corners
            let transformedTopLeft = matrix.transform(point: topLeft)
            let transformedTopRight = matrix.transform(point: topRight)
            let transformedBottomLeft = matrix.transform(point: bottomLeft)
            let transformedBottomRight = matrix.transform(point: bottomRight)

            // Find the bounding rect
            let minX = min(transformedTopLeft.x, transformedTopRight.x, transformedBottomLeft.x, transformedBottomRight.x)
            let minY = min(transformedTopLeft.y, transformedTopRight.y, transformedBottomLeft.y, transformedBottomRight.y)
            let maxX = max(transformedTopLeft.x, transformedTopRight.x, transformedBottomLeft.x, transformedBottomRight.x)
            let maxY = max(transformedTopLeft.y, transformedTopRight.y, transformedBottomLeft.y, transformedBottomRight.y)

            return CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
        }

        // Fall back to basic computation
        return computeOverlayFrame(overlay: overlay, in: mapView)
    }

}