import SwiftUI
import MapKit

struct MapViewRepresentable: UIViewRepresentable {
    @ObservedObject var viewModel: MapViewModel
    var overlays: [Overlay]

    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView(frame: .zero)
        mapView.showsUserLocation = true
        viewModel.setMapView(mapView)
        return mapView
    }

    func updateUIView(_ uiView: MKMapView, context: Context) {
        uiView.removeOverlays(uiView.overlays)
        guard viewModel.showOverlays else { return }
        for overlay in overlays where overlay.isVisible {
            if let url = overlay.imageURL {
                let mapOverlay = ImageOverlay(overlay: overlay, imageURL: url)
                uiView.addOverlay(mapOverlay)
            }
        }
    }

}

class ImageOverlay: NSObject, MKOverlay {
    let overlayData: Overlay
    let imageURL: URL

    init(overlay: Overlay, imageURL: URL) {
        self.overlayData = overlay
        self.imageURL = imageURL
    }

    var coordinate: CLLocationCoordinate2D {
        CLLocationCoordinate2D(latitude: overlayData.centerLatitude, longitude: overlayData.centerLongitude)
    }

    var boundingMapRect: MKMapRect {
        let size = MKMapSize(width: overlayData.relativeScale, height: overlayData.relativeScale)
        let centerPoint = MKMapPoint(coordinate)
        let origin = MKMapPoint(x: centerPoint.x - size.width/2, y: centerPoint.y - size.height/2)
        return MKMapRect(origin: origin, size: size)
    }
}

class ImageOverlayRenderer: MKOverlayRenderer {
    var image: UIImage?

    override init(overlay: MKOverlay) {
        if let imageOverlay = overlay as? ImageOverlay {
            self.image = UIImage(contentsOfFile: imageOverlay.imageURL.path)
        }
        super.init(overlay: overlay)
    }

    override func draw(_ mapRect: MKMapRect, zoomScale: MKZoomScale, in context: CGContext) {
        guard let image = image else { return }
        let rect = self.rect(for: overlay.boundingMapRect)
        context.saveGState()
        context.translateBy(x: rect.midX, y: rect.midY)
        if let imageOverlay = overlay as? ImageOverlay {
            context.rotate(by: imageOverlay.overlayData.rotation)
            context.setAlpha(CGFloat(imageOverlay.overlayData.opacity))
        }
        context.translateBy(x: -rect.size.width/2, y: -rect.size.height/2)
        context.draw(image.cgImage!, in: CGRect(origin: .zero, size: rect.size))
        context.restoreGState()
    }
}
