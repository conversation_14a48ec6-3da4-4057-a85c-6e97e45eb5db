import SwiftUI
import MapKit

struct MapViewRepresentable: UIViewRepresentable {
    @ObservedObject var viewModel: MapViewModel
    var overlays: [Overlay]

    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView(frame: .zero)
        mapView.showsUserLocation = true
        mapView.delegate = viewModel  // Set delegate to enable overlay rendering
        viewModel.setMapView(mapView)
        if let region = viewModel.currentRegion {
            mapView.setRegion(region, animated: false)
        }
        return mapView
    }

    func updateUIView(_ uiView: MKMapView, context: Context) {
        // Ensure delegate is still set
        if uiView.delegate !== viewModel {
            uiView.delegate = viewModel
        }

        uiView.removeOverlays(uiView.overlays)
        guard viewModel.showOverlays else { return }
        for overlay in overlays where overlay.isVisible {
            if let url = overlay.imageURL {
                let mapOverlay = ImageOverlay(overlay: overlay, imageURL: url)
                uiView.addOverlay(mapOverlay)
                print("Added overlay: \(overlay.name ?? "Unnamed") at \(overlay.centerLatitude), \(overlay.centerLongitude)")
            }
        }
    }

}

class ImageOverlay: NSObject, MKOverlay {
    let overlayData: Overlay
    let imageURL: URL

    init(overlay: Overlay, imageURL: URL) {
        self.overlayData = overlay
        self.imageURL = imageURL
    }

    var coordinate: CLLocationCoordinate2D {
        CLLocationCoordinate2D(latitude: overlayData.centerLatitude, longitude: overlayData.centerLongitude)
    }

    var boundingMapRect: MKMapRect {
        // `relativeScale` is stored in metres but `MKMapRect` expects map
        // points. Convert the dimension using the current latitude so the
        // overlay renders at the expected size.
        let metersPerPoint = MKMetersPerMapPointAtLatitude(coordinate.latitude)
        let sideLengthPoints = overlayData.relativeScale / metersPerPoint

        let size = MKMapSize(width: sideLengthPoints, height: sideLengthPoints)

        let centerPoint = MKMapPoint(coordinate)
        let origin = MKMapPoint(x: centerPoint.x - size.width / 2,
                                y: centerPoint.y - size.height / 2)
        let rect = MKMapRect(origin: origin, size: size)
        return rect
    }
}

class ImageOverlayRenderer: MKOverlayRenderer {
    var image: UIImage?

    override init(overlay: MKOverlay) {
        super.init(overlay: overlay)
        if let imageOverlay = overlay as? ImageOverlay {
            // Try multiple ways to load the image
            if let loadedImage = UIImage(contentsOfFile: imageOverlay.imageURL.path) {
                self.image = loadedImage
                print("Successfully loaded image from: \(imageOverlay.imageURL.path)")
            } else if let data = try? Data(contentsOf: imageOverlay.imageURL),
                      let loadedImage = UIImage(data: data) {
                self.image = loadedImage
                print("Successfully loaded image data from: \(imageOverlay.imageURL)")
            } else {
                print("Failed to load image from: \(imageOverlay.imageURL)")
            }
        }
    }

    override func draw(_ mapRect: MKMapRect, zoomScale: MKZoomScale, in context: CGContext) {
        guard let image = image else {
            print("No image available for rendering")
            return
        }

        let rect = self.rect(for: overlay.boundingMapRect)
        context.saveGState()
        context.translateBy(x: rect.midX, y: rect.midY)
        if let imageOverlay = overlay as? ImageOverlay {
            context.rotate(by: imageOverlay.overlayData.rotation)
            context.setAlpha(CGFloat(imageOverlay.overlayData.opacity))
        }
        context.translateBy(x: -rect.size.width/2, y: -rect.size.height/2)

        if let cgImage = image.cgImage {
            context.draw(cgImage, in: CGRect(origin: .zero, size: rect.size))
        }
        context.restoreGState()
    }
}
