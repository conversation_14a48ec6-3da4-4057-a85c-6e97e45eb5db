import SwiftUI
import MapKit

struct MapViewRepresentable: UIViewRepresentable {
    @ObservedObject var viewModel: MapViewModel
    var overlays: [Overlay]

    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView(frame: .zero)
        mapView.showsUserLocation = true
        mapView.delegate = viewModel  // Set delegate to enable overlay rendering
        viewModel.setMapView(mapView)
        if let region = viewModel.currentRegion {
            mapView.setRegion(region, animated: false)
        }
        return mapView
    }

    func updateUIView(_ uiView: MKMapView, context: Context) {
        // Ensure delegate is still set
        if uiView.delegate !== viewModel {
            uiView.delegate = viewModel
        }

        // Remove existing overlays
        uiView.removeOverlays(uiView.overlays)

        guard viewModel.showOverlays else { return }

        // Add visible overlays
        for overlay in overlays where overlay.isVisible {
            let mapOverlay = ImageOverlay(overlayData: overlay)
            uiView.addOverlay(mapOverlay)
            print("Added overlay: \(overlay.name ?? "Unnamed") at \(overlay.centerLatitude), \(overlay.centerLongitude)")
        }
    }
}

/// Custom MKOverlay implementation for image overlays
class ImageOverlay: NSObject, MKOverlay {
    // The Core Data overlay entity
    let overlayData: Overlay

    // Cached image for performance
    private var cachedImage: UIImage?

    // Cached bounds
    private var cachedBounds: MKMapRect?

    init(overlayData: Overlay) {
        self.overlayData = overlayData
        super.init()
    }

    // MARK: - MKOverlay Protocol

    var coordinate: CLLocationCoordinate2D {
        CLLocationCoordinate2D(latitude: overlayData.centerLatitude, longitude: overlayData.centerLongitude)
    }

    var boundingMapRect: MKMapRect {
        // If we have cached bounds, use them
        if let cachedBounds = cachedBounds {
            return cachedBounds
        }

        // Calculate bounds based on the overlay's scale and position
        let center = MKMapPoint(coordinate)

        // Convert scale from meters to map points
        let metersPerPoint = MKMetersPerMapPointAtLatitude(coordinate.latitude)
        let sizeInPoints = overlayData.relativeScale / metersPerPoint

        // Create the bounding rect
        let rect = MKMapRect(
            x: center.x - sizeInPoints / 2,
            y: center.y - sizeInPoints / 2,
            width: sizeInPoints,
            height: sizeInPoints
        )

        // Cache this rect
        cachedBounds = rect
        return rect
    }

    // MARK: - Image Loading

    /// Get the image for this overlay, loading it if necessary
    func getImage() -> UIImage? {
        if let cachedImage = cachedImage {
            return cachedImage
        }

        // Load the image using ImageManager
        if let image = ImageManager.shared.loadImage(from: overlayData.imageURL) {
            cachedImage = image
            return image
        }

        return nil
    }

    /// Clear the cached image to force reload
    func clearImageCache() {
        cachedImage = nil
    }
}

/// Renderer for image overlays with improved performance and transformation support
class ImageOverlayRenderer: MKOverlayRenderer {

    override init(overlay: MKOverlay) {
        super.init(overlay: overlay)
    }

    override func draw(_ mapRect: MKMapRect, zoomScale: MKZoomScale, in context: CGContext) {
        guard let imageOverlay = overlay as? ImageOverlay else {
            print("ImageOverlayRenderer: Overlay is not an ImageOverlay")
            return
        }

        guard let image = imageOverlay.getImage() else {
            print("ImageOverlayRenderer: No image available for overlay '\(imageOverlay.overlayData.name ?? "Unnamed")'")
            return
        }

        // Get the rect for the overlay in the current view
        let overlayRect = rect(for: overlay.boundingMapRect)

        // Check if the overlay rect intersects with the map rect being drawn
        let drawRect = rect(for: mapRect)
        guard overlayRect.intersects(drawRect) else {
            return // No need to draw if not visible
        }

        // Save the graphics state
        context.saveGState()

        // Set up the transformation
        setupTransformation(context: context, overlayRect: overlayRect, imageOverlay: imageOverlay)

        // Set the opacity
        context.setAlpha(CGFloat(imageOverlay.overlayData.opacity))

        // Draw the image
        if let cgImage = image.cgImage {
            // Calculate the image rect (centered and scaled)
            let imageSize = image.size
            let aspectRatio = imageSize.width / imageSize.height

            var imageRect: CGRect
            if aspectRatio > 1.0 {
                // Landscape image
                let height = overlayRect.height
                let width = height * aspectRatio
                imageRect = CGRect(
                    x: -width / 2,
                    y: -height / 2,
                    width: width,
                    height: height
                )
            } else {
                // Portrait or square image
                let width = overlayRect.width
                let height = width / aspectRatio
                imageRect = CGRect(
                    x: -width / 2,
                    y: -height / 2,
                    width: width,
                    height: height
                )
            }

            context.draw(cgImage, in: imageRect)
        }

        // Restore the graphics state
        context.restoreGState()
    }

    /// Set up the transformation matrix for the overlay
    private func setupTransformation(context: CGContext, overlayRect: CGRect, imageOverlay: ImageOverlay) {
        // Move to the center of the overlay
        context.translateBy(x: overlayRect.midX, y: overlayRect.midY)

        // Apply rotation if any
        let rotation = imageOverlay.overlayData.rotation
        if rotation != 0 {
            context.rotate(by: CGFloat(rotation))
        }

        // Apply any additional scaling based on the relative scale
        let scale = CGFloat(imageOverlay.overlayData.relativeScale / 1000.0) // Normalize scale
        if scale != 1.0 && scale > 0 {
            context.scaleBy(x: scale, y: scale)
        }
    }
}
