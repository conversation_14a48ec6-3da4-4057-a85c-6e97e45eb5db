import CoreLocation
import CoreGraphics

/// Represents a pair of points: one in screen/image coordinates and one in geographic coordinates
struct PointPair: Codable {
    /// Point in screen/image coordinates (pixels)
    let screenPoint: CGPoint

    /// Corresponding geographic coordinate
    let coordinate: CLLocationCoordinate2D

    init(screenPoint: CGPoint, coordinate: CLLocationCoordinate2D) {
        self.screenPoint = screenPoint
        self.coordinate = coordinate
    }
}

// MARK: - Equatable
extension PointPair: Equatable {
    static func == (lhs: PointPair, rhs: PointPair) -> Bool {
        return lhs.screenPoint == rhs.screenPoint &&
               lhs.coordinate.latitude == rhs.coordinate.latitude &&
               lhs.coordinate.longitude == rhs.coordinate.longitude
    }
}

// MARK: - Codable Support for CLLocationCoordinate2D
extension CLLocationCoordinate2D: Codable {
    enum CodingKeys: String, CodingKey {
        case latitude
        case longitude
    }

    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let latitude = try container.decode(Double.self, forKey: .latitude)
        let longitude = try container.decode(Double.self, forKey: .longitude)
        self.init(latitude: latitude, longitude: longitude)
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(latitude, forKey: .latitude)
        try container.encode(longitude, forKey: .longitude)
    }
}
