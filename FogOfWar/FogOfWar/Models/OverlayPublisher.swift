import SwiftUI
import Combine

/// Publisher for overlay changes to coordinate updates across the app
class OverlayPublisher: ObservableObject {
    @Published var overlayUpdated = UUID()
    @Published var selectedOverlay: Overlay?
    @Published var isEditingOverlay = false
    
    /// Notify that an overlay has changed and views should refresh
    func overlayDidChange() {
        DispatchQueue.main.async {
            self.overlayUpdated = UUID()
        }
    }
    
    /// Start editing an overlay
    func startEditing(_ overlay: Overlay) {
        selectedOverlay = overlay
        isEditingOverlay = true
    }
    
    /// Stop editing overlay
    func stopEditing() {
        selectedOverlay = nil
        isEditingOverlay = false
    }
}
