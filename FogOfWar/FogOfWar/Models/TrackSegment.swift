import CoreLocation
import Foundation

/// Represents a segment of a track being recorded
class TrackSegment: ObservableObject {
    /// The track this segment belongs to
    weak var track: Track?
    
    /// Array of locations in this segment
    @Published var locations: [CLLocation] = []
    
    /// When this segment started recording
    let startTime: Date
    
    /// Current status of the segment
    @Published var isRecording: Bool = false
    
    /// Total distance covered in this segment (in meters)
    @Published var distance: Double = 0.0
    
    /// Duration of this segment (in seconds)
    var duration: TimeInterval {
        guard let lastLocation = locations.last else { return 0 }
        return lastLocation.timestamp.timeIntervalSince(startTime)
    }
    
    init(track: Track? = nil) {
        self.track = track
        self.startTime = Date()
    }
    
    /// Add a new location to this segment
    func addLocation(_ location: CLLocation) {
        // Calculate distance from previous location
        if let lastLocation = locations.last {
            let segmentDistance = location.distance(from: lastLocation)
            distance += segmentDistance
        }
        
        locations.append(location)
        
        // Update the associated track if it exists
        if let track = track {
            // Create a new TrackPoint for Core Data
            let trackPoint = TrackPoint(context: track.managedObjectContext!)
            trackPoint.latitude = location.coordinate.latitude
            trackPoint.longitude = location.coordinate.longitude
            trackPoint.altitude = location.altitude
            trackPoint.horizontalAccuracy = location.horizontalAccuracy
            trackPoint.timestamp = location.timestamp
            trackPoint.track = track
            
            // Update track statistics
            track.lengthMetres += distance
            track.durationSeconds = duration
            track.endedAt = location.timestamp
            
            // Save the context
            try? track.managedObjectContext?.save()
        }
    }
    
    /// Start recording this segment
    func startRecording() {
        isRecording = true
    }
    
    /// Stop recording this segment
    func stopRecording() {
        isRecording = false
        
        // Update the track's end time
        if let track = track, let lastLocation = locations.last {
            track.endedAt = lastLocation.timestamp
            try? track.managedObjectContext?.save()
        }
    }
    
    /// Get the current speed in m/s
    var currentSpeed: Double {
        guard let lastLocation = locations.last else { return 0 }
        return lastLocation.speed >= 0 ? lastLocation.speed : 0
    }
    
    /// Get the average speed in m/s
    var averageSpeed: Double {
        guard duration > 0 else { return 0 }
        return distance / duration
    }
    
    /// Get the current altitude
    var currentAltitude: Double {
        guard let lastLocation = locations.last else { return 0 }
        return lastLocation.altitude
    }
    
    /// Get the current coordinate
    var currentCoordinate: CLLocationCoordinate2D? {
        return locations.last?.coordinate
    }
}
