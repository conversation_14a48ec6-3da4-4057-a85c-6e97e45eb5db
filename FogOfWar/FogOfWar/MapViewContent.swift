import SwiftUI
import CoreData

struct MapViewContent: View {
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Overlay.createdAt, ascending: true)],
        animation: .default)
    private var overlays: FetchedResults<Overlay>

    @StateObject private var viewModel = MapViewModel()
    @State private var showingLayerPanel = false
    @State private var showingQuickAdd = false

    var body: some View {
        ZStack(alignment: .bottomTrailing) {
            MapViewRepresentable(viewModel: viewModel, overlays: Array(overlays))
                .ignoresSafeArea()
            VStack {
                Button(action: { viewModel.recenterUser() }) {
                    Image(systemName: "location.fill")
                        .padding(8)
                        .background(.thinMaterial, in: Circle())
                }
                Button(action: { showingLayerPanel = true }) {
                    Image(systemName: "line.3.horizontal.decrease.circle")
                        .padding(8)
                        .background(.thinMaterial, in: Circle())
                }
                But<PERSON>(action: { showingQuickAdd = true }) {
                    Image(systemName: "plus")
                        .padding(8)
                        .background(.thinMaterial, in: Circle())
                }
            }
            .padding()
        }
        .sheet(isPresented: $showingQuickAdd) {
            OverlayManager()
        }
        .sheet(isPresented: $showingLayerPanel) {
            Text("Layer Panel")
                .presentationDetents([.medium])
        }
        .navigationTitle("Map")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Text(viewModel.scaleText)
                    .monospacedDigit()
            }
        }
    }
}
