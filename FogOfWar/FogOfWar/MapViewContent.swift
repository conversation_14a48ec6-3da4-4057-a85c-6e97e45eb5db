import SwiftUI
import CoreData

struct MapViewContent: View {
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Overlay.createdAt, ascending: true)],
        animation: .default)
    private var overlays: FetchedResults<Overlay>

    @StateObject private var viewModel = MapViewModel()
    @State private var showingLayerPanel = false
    @State private var showingQuickAdd = false

    var body: some View {
        ZStack(alignment: .topTrailing) {
            MapViewRepresentable(viewModel: viewModel, overlays: Array(overlays))
                .ignoresSafeArea()
            VStack(spacing: 16) {
                But<PERSON>(action: { viewModel.recenterUser() }) {
                    Image(systemName: "location.fill")
                        .font(.title2)
                        .padding(12)
                        .background(.thinMaterial, in: Circle())
                }
                But<PERSON>(action: { showingLayerPanel = true }) {
                    Image(systemName: "line.3.horizontal.decrease.circle")
                        .font(.title2)
                        .padding(12)
                        .background(.thinMaterial, in: Circle())
                }
                Button(action: { showingQuickAdd = true }) {
                    Image(systemName: "plus")
                        .font(.title2)
                        .padding(12)
                        .background(.thinMaterial, in: Circle())
                }
            }
            .padding(.top, 60)
            .padding(.trailing)
        }
        .sheet(isPresented: $showingQuickAdd) {
            OverlayManager(viewModel: viewModel)
        }
        .sheet(isPresented: $showingLayerPanel) {
            Text("Layer Panel")
                .presentationDetents([.medium])
        }
        .navigationTitle("Map")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Text(viewModel.scaleText)
                    .monospacedDigit()
            }
        }
    }
}
