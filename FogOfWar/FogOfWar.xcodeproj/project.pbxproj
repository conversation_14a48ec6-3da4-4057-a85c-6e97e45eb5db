// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		61E215332DEE6E09000E93F4 /* LICENSE in Resources */ = {isa = PBXBuildFile; fileRef = 61E215312DEE6E09000E93F4 /* LICENSE */; };
		61E215342DEE6E09000E93F4 /* Requirements.md in Resources */ = {isa = PBXBuildFile; fileRef = 61E215322DEE6E09000E93F4 /* Requirements.md */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		61E215142DEE6D9E000E93F4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 61E214F72DEE6D9D000E93F4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 61E214FE2DEE6D9D000E93F4;
			remoteInfo = FogOfWar;
		};
		61E2151E2DEE6D9E000E93F4 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 61E214F72DEE6D9D000E93F4 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 61E214FE2DEE6D9D000E93F4;
			remoteInfo = FogOfWar;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		61E214FF2DEE6D9D000E93F4 /* FogOfWar.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = FogOfWar.app; sourceTree = BUILT_PRODUCTS_DIR; };
		61E215132DEE6D9E000E93F4 /* FogOfWarTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = FogOfWarTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		61E2151D2DEE6D9E000E93F4 /* FogOfWarUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = FogOfWarUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		61E215312DEE6E09000E93F4 /* LICENSE */ = {isa = PBXFileReference; lastKnownFileType = text; path = LICENSE; sourceTree = "<group>"; };
		61E215322DEE6E09000E93F4 /* Requirements.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = Requirements.md; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		61E215252DEE6D9E000E93F4 /* Exceptions for "FogOfWar" folder in "FogOfWar" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 61E214FE2DEE6D9D000E93F4 /* FogOfWar */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		61E215012DEE6D9D000E93F4 /* FogOfWar */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				61E215252DEE6D9E000E93F4 /* Exceptions for "FogOfWar" folder in "FogOfWar" target */,
			);
			path = FogOfWar;
			sourceTree = "<group>";
		};
		61E215162DEE6D9E000E93F4 /* FogOfWarTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = FogOfWarTests;
			sourceTree = "<group>";
		};
		61E215202DEE6D9E000E93F4 /* FogOfWarUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = FogOfWarUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		61E214FC2DEE6D9D000E93F4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		61E215102DEE6D9E000E93F4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		61E2151A2DEE6D9E000E93F4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		61E214F62DEE6D9D000E93F4 = {
			isa = PBXGroup;
			children = (
				61E215312DEE6E09000E93F4 /* LICENSE */,
				61E215322DEE6E09000E93F4 /* Requirements.md */,
				61E215012DEE6D9D000E93F4 /* FogOfWar */,
				61E215162DEE6D9E000E93F4 /* FogOfWarTests */,
				61E215202DEE6D9E000E93F4 /* FogOfWarUITests */,
				61E215002DEE6D9D000E93F4 /* Products */,
			);
			sourceTree = "<group>";
		};
		61E215002DEE6D9D000E93F4 /* Products */ = {
			isa = PBXGroup;
			children = (
				61E214FF2DEE6D9D000E93F4 /* FogOfWar.app */,
				61E215132DEE6D9E000E93F4 /* FogOfWarTests.xctest */,
				61E2151D2DEE6D9E000E93F4 /* FogOfWarUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		61E214FE2DEE6D9D000E93F4 /* FogOfWar */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 61E215262DEE6D9E000E93F4 /* Build configuration list for PBXNativeTarget "FogOfWar" */;
			buildPhases = (
				61E214FB2DEE6D9D000E93F4 /* Sources */,
				61E214FC2DEE6D9D000E93F4 /* Frameworks */,
				61E214FD2DEE6D9D000E93F4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				61E215012DEE6D9D000E93F4 /* FogOfWar */,
			);
			name = FogOfWar;
			packageProductDependencies = (
			);
			productName = FogOfWar;
			productReference = 61E214FF2DEE6D9D000E93F4 /* FogOfWar.app */;
			productType = "com.apple.product-type.application";
		};
		61E215122DEE6D9E000E93F4 /* FogOfWarTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 61E2152B2DEE6D9E000E93F4 /* Build configuration list for PBXNativeTarget "FogOfWarTests" */;
			buildPhases = (
				61E2150F2DEE6D9E000E93F4 /* Sources */,
				61E215102DEE6D9E000E93F4 /* Frameworks */,
				61E215112DEE6D9E000E93F4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				61E215152DEE6D9E000E93F4 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				61E215162DEE6D9E000E93F4 /* FogOfWarTests */,
			);
			name = FogOfWarTests;
			packageProductDependencies = (
			);
			productName = FogOfWarTests;
			productReference = 61E215132DEE6D9E000E93F4 /* FogOfWarTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		61E2151C2DEE6D9E000E93F4 /* FogOfWarUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 61E2152E2DEE6D9E000E93F4 /* Build configuration list for PBXNativeTarget "FogOfWarUITests" */;
			buildPhases = (
				61E215192DEE6D9E000E93F4 /* Sources */,
				61E2151A2DEE6D9E000E93F4 /* Frameworks */,
				61E2151B2DEE6D9E000E93F4 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				61E2151F2DEE6D9E000E93F4 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				61E215202DEE6D9E000E93F4 /* FogOfWarUITests */,
			);
			name = FogOfWarUITests;
			packageProductDependencies = (
			);
			productName = FogOfWarUITests;
			productReference = 61E2151D2DEE6D9E000E93F4 /* FogOfWarUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		61E214F72DEE6D9D000E93F4 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					61E214FE2DEE6D9D000E93F4 = {
						CreatedOnToolsVersion = 16.4;
					};
					61E215122DEE6D9E000E93F4 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 61E214FE2DEE6D9D000E93F4;
					};
					61E2151C2DEE6D9E000E93F4 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 61E214FE2DEE6D9D000E93F4;
					};
				};
			};
			buildConfigurationList = 61E214FA2DEE6D9D000E93F4 /* Build configuration list for PBXProject "FogOfWar" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 61E214F62DEE6D9D000E93F4;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 61E215002DEE6D9D000E93F4 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				61E214FE2DEE6D9D000E93F4 /* FogOfWar */,
				61E215122DEE6D9E000E93F4 /* FogOfWarTests */,
				61E2151C2DEE6D9E000E93F4 /* FogOfWarUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		61E214FD2DEE6D9D000E93F4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				61E215332DEE6E09000E93F4 /* LICENSE in Resources */,
				61E215342DEE6E09000E93F4 /* Requirements.md in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		61E215112DEE6D9E000E93F4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		61E2151B2DEE6D9E000E93F4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		61E214FB2DEE6D9D000E93F4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		61E2150F2DEE6D9E000E93F4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		61E215192DEE6D9E000E93F4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		61E215152DEE6D9E000E93F4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 61E214FE2DEE6D9D000E93F4 /* FogOfWar */;
			targetProxy = 61E215142DEE6D9E000E93F4 /* PBXContainerItemProxy */;
		};
		61E2151F2DEE6D9E000E93F4 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 61E214FE2DEE6D9D000E93F4 /* FogOfWar */;
			targetProxy = 61E2151E2DEE6D9E000E93F4 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		61E215272DEE6D9E000E93F4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = FogOfWar/FogOfWar.entitlements;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = KF6N75TM5M;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = FogOfWar/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = FogOfWar;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "Map needs your location.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "Import overlay images.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Import overlay images.";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.brummblebee.FogOfWar;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = FogOfWar_AppStore;
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		61E215282DEE6D9E000E93F4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = FogOfWar/FogOfWar.entitlements;
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = KF6N75TM5M;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = FogOfWar/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = FogOfWar;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.lifestyle";
				INFOPLIST_KEY_NSLocationWhenInUseUsageDescription = "Map needs your location.";
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "Import overlay images.";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "Import overlay images.";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.brummblebee.FogOfWar;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = FogOfWar_AppStore;
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		61E215292DEE6D9E000E93F4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = KF6N75TM5M;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		61E2152A2DEE6D9E000E93F4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = KF6N75TM5M;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		61E2152C2DEE6D9E000E93F4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = KF6N75TM5M;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = brummblebee.FogOfWarTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/FogOfWar.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/FogOfWar";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		61E2152D2DEE6D9E000E93F4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = KF6N75TM5M;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = brummblebee.FogOfWarTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/FogOfWar.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/FogOfWar";
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
		61E2152F2DEE6D9E000E93F4 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = KF6N75TM5M;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = brummblebee.FogOfWarUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = FogOfWar;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Debug;
		};
		61E215302DEE6D9E000E93F4 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = KF6N75TM5M;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MACOSX_DEPLOYMENT_TARGET = 15.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = brummblebee.FogOfWarUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx xros xrsimulator";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				TEST_TARGET_NAME = FogOfWar;
				XROS_DEPLOYMENT_TARGET = 2.5;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		61E214FA2DEE6D9D000E93F4 /* Build configuration list for PBXProject "FogOfWar" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				61E215292DEE6D9E000E93F4 /* Debug */,
				61E2152A2DEE6D9E000E93F4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		61E215262DEE6D9E000E93F4 /* Build configuration list for PBXNativeTarget "FogOfWar" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				61E215272DEE6D9E000E93F4 /* Debug */,
				61E215282DEE6D9E000E93F4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		61E2152B2DEE6D9E000E93F4 /* Build configuration list for PBXNativeTarget "FogOfWarTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				61E2152C2DEE6D9E000E93F4 /* Debug */,
				61E2152D2DEE6D9E000E93F4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		61E2152E2DEE6D9E000E93F4 /* Build configuration list for PBXNativeTarget "FogOfWarUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				61E2152F2DEE6D9E000E93F4 /* Debug */,
				61E215302DEE6D9E000E93F4 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 61E214F72DEE6D9D000E93F4 /* Project object */;
}
