import MapKit

/// Custom MKOverlay implementation for image overlays
class ImageOverlay: NSObject, MKOverlay {
    // The Core Data overlay entity
    let overlayData: Overlay

    // Cached bounds
    private var cachedBounds: MKMapRect?

    init(overlayData: Overlay) {
        self.overlayData = overlayData
        super.init()
    }

    // MARK: - MKOverlay Protocol

    var coordinate: CLLocationCoordinate2D {
        CLLocationCoordinate2D(latitude: overlayData.centerLat, longitude: overlayData.centerLon)
    }

    var boundingMapRect: MKMapRect {
        // If we have cached bounds, use them
        if let cachedBounds = cachedBounds {
            return cachedBounds
        }

        // Otherwise, create a large enough rect to contain the overlay
        // This is a fallback and will be refined when rendered
        let center = MKMapPoint(coordinate)

        // Use a larger size to ensure the overlay is visible
        // This is just an initial size that will be refined when the overlay is rendered
        let size: Double = 10000  // 10000 map points is a reasonable starting size

        // Create a large bounding rect
        let rect = MKMapRect(
            x: center.x - size / 2,
            y: center.y - size / 2,
            width: size,
            height: size
        )

        // Cache this rect
        cachedBounds = rect

        return rect
    }

    // MARK: - Utility Methods

    /// Update the cached bounds based on the computed frame
    func updateBounds(with frame: CGRect, in mapView: MKMapView) {
        // Convert the frame corners to map coordinates
        let topLeft = mapView.convert(CGPoint(x: frame.minX, y: frame.minY), toCoordinateFrom: mapView)
        let topRight = mapView.convert(CGPoint(x: frame.maxX, y: frame.minY), toCoordinateFrom: mapView)
        let bottomLeft = mapView.convert(CGPoint(x: frame.minX, y: frame.maxY), toCoordinateFrom: mapView)
        let bottomRight = mapView.convert(CGPoint(x: frame.maxX, y: frame.maxY), toCoordinateFrom: mapView)

        // Convert to map points
        let points = [
            MKMapPoint(topLeft),
            MKMapPoint(topRight),
            MKMapPoint(bottomLeft),
            MKMapPoint(bottomRight)
        ]

        // Find the bounding rect
        var minX = Double.infinity
        var minY = Double.infinity
        var maxX = -Double.infinity
        var maxY = -Double.infinity

        for point in points {
            minX = min(minX, point.x)
            minY = min(minY, point.y)
            maxX = max(maxX, point.x)
            maxY = max(maxY, point.y)
        }

        // Calculate the width and height
        let width = maxX - minX
        let height = maxY - minY

        // Use a moderate expansion to ensure the overlay is visible
        // Add a buffer around the actual image bounds
        let expandedMinX = minX - width * 0.5
        let expandedMinY = minY - height * 0.5
        let expandedWidth = width * 2.0  // 2x the original width
        let expandedHeight = height * 2.0  // 2x the original height

        // Create the map rect with expanded bounds
        cachedBounds = MKMapRect(
            x: expandedMinX,
            y: expandedMinY,
            width: expandedWidth,
            height: expandedHeight
        )
    }
}
