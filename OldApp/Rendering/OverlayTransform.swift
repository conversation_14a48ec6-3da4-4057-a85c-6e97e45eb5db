import MapKit
import CoreGraphics
import simd

enum TransformError: Error {
    case insufficientControlPoints
    case singularMatrix
    case invalidInput
}

class OverlayTransform {
    static func computeOverlayFrame(overlay: Overlay, in mapView: MKMapView) -> CGRect {
        // Extract control points
        let controlPoints = overlay.decodedControlPoints

        // Load the image to get its size
        var imageSize = CGSize(width: 1000, height: 1000) // Default size

        // Use ImageManager to load the image and get its size
        if let image = ImageManager.shared.loadImage(from: overlay.imageURL) {
            imageSize = image.size

            // Make sure the overlay has the correct URL
            ImageManager.shared.updateOverlayImageURL(overlay)
        }

        // If we have no control points, use basic parameters
        if controlPoints.isEmpty {
            // Create a basic frame based on center, scale, and rotation
            let center = mapView.convert(
                CLLocationCoordinate2D(latitude: overlay.centerLat, longitude: overlay.centerLon),
                toPointTo: mapView
            )

            // Apply rotation and scale to all four corners
            let topLeft = CGPoint(x: -imageSize.width/2, y: -imageSize.height/2)
            let topRight = CGPoint(x: imageSize.width/2, y: -imageSize.height/2)
            let bottomLeft = CGPoint(x: -imageSize.width/2, y: imageSize.height/2)
            let bottomRight = CGPoint(x: imageSize.width/2, y: imageSize.height/2)

            // Create a transform
            var transform = CGAffineTransform.identity
            transform = transform.translatedBy(x: center.x, y: center.y)
            transform = transform.rotated(by: CGFloat(overlay.rotation))
            transform = transform.scaledBy(x: CGFloat(overlay.relScale), y: CGFloat(overlay.relScale))

            // Apply transform to all corners
            let transformedTopLeft = topLeft.applying(transform)
            let transformedTopRight = topRight.applying(transform)
            let transformedBottomLeft = bottomLeft.applying(transform)
            let transformedBottomRight = bottomRight.applying(transform)

            // Find the bounding rect
            let minX = min(transformedTopLeft.x, transformedTopRight.x, transformedBottomLeft.x, transformedBottomRight.x)
            let minY = min(transformedTopLeft.y, transformedTopRight.y, transformedBottomLeft.y, transformedBottomRight.y)
            let maxX = max(transformedTopLeft.x, transformedTopRight.x, transformedBottomLeft.x, transformedBottomRight.x)
            let maxY = max(transformedTopLeft.y, transformedTopRight.y, transformedBottomLeft.y, transformedBottomRight.y)

            return CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
        }

        // If we have a transform matrix, use it
        if let transformData = overlay.transform,
           let matrix = try? JSONDecoder().decode(Matrix3x3.self, from: transformData) {

            // Apply the matrix to all four corners of the image
            let topLeft = CGPoint(x: 0, y: 0)
            let topRight = CGPoint(x: imageSize.width, y: 0)
            let bottomLeft = CGPoint(x: 0, y: imageSize.height)
            let bottomRight = CGPoint(x: imageSize.width, y: imageSize.height)

            // Transform all corners
            let transformedTopLeft = matrix.transform(point: topLeft)
            let transformedTopRight = matrix.transform(point: topRight)
            let transformedBottomLeft = matrix.transform(point: bottomLeft)
            let transformedBottomRight = matrix.transform(point: bottomRight)

            // Find the bounding rect
            let minX = min(transformedTopLeft.x, transformedTopRight.x, transformedBottomLeft.x, transformedBottomRight.x)
            let minY = min(transformedTopLeft.y, transformedTopRight.y, transformedBottomLeft.y, transformedBottomRight.y)
            let maxX = max(transformedTopLeft.x, transformedTopRight.x, transformedBottomLeft.x, transformedBottomRight.x)
            let maxY = max(transformedTopLeft.y, transformedTopRight.y, transformedBottomLeft.y, transformedBottomRight.y)

            return CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
        }

        // If we have control points but no transform, compute it
        do {
            // Convert control points to map points
            var mapControlPoints: [PointPair] = []
            for point in controlPoints {
                let mapPoint = mapView.convert(point.coordinate, toPointTo: mapView)
                let mapPair = PointPair(
                    screenPoint: point.screenPoint,
                    coordinate: CLLocationCoordinate2D(latitude: mapPoint.y, longitude: mapPoint.x)
                )
                mapControlPoints.append(mapPair)
            }

            // Compute the transform matrix
            let transformData = try computeTransformMatrix(controlPoints: mapControlPoints)
            let matrix = try JSONDecoder().decode(Matrix3x3.self, from: transformData)

            // Apply the matrix to all four corners of the image
            let topLeft = CGPoint(x: 0, y: 0)
            let topRight = CGPoint(x: imageSize.width, y: 0)
            let bottomLeft = CGPoint(x: 0, y: imageSize.height)
            let bottomRight = CGPoint(x: imageSize.width, y: imageSize.height)

            // Transform all corners
            let transformedTopLeft = matrix.transform(point: topLeft)
            let transformedTopRight = matrix.transform(point: topRight)
            let transformedBottomLeft = matrix.transform(point: bottomLeft)
            let transformedBottomRight = matrix.transform(point: bottomRight)

            // Find the bounding rect
            let minX = min(transformedTopLeft.x, transformedTopRight.x, transformedBottomLeft.x, transformedBottomRight.x)
            let minY = min(transformedTopLeft.y, transformedTopRight.y, transformedBottomLeft.y, transformedBottomRight.y)
            let maxX = max(transformedTopLeft.x, transformedTopRight.x, transformedBottomLeft.x, transformedBottomRight.x)
            let maxY = max(transformedTopLeft.y, transformedTopRight.y, transformedBottomLeft.y, transformedBottomRight.y)

            return CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
        } catch {
            print("Error computing transform: \(error)")

            // Fall back to basic parameters
            let center = mapView.convert(
                CLLocationCoordinate2D(latitude: overlay.centerLat, longitude: overlay.centerLon),
                toPointTo: mapView
            )

            // Apply rotation and scale to all four corners
            let topLeft = CGPoint(x: -imageSize.width/2, y: -imageSize.height/2)
            let topRight = CGPoint(x: imageSize.width/2, y: -imageSize.height/2)
            let bottomLeft = CGPoint(x: -imageSize.width/2, y: imageSize.height/2)
            let bottomRight = CGPoint(x: imageSize.width/2, y: imageSize.height/2)

            // Create a transform
            var transform = CGAffineTransform.identity
            transform = transform.translatedBy(x: center.x, y: center.y)
            transform = transform.rotated(by: CGFloat(overlay.rotation))
            transform = transform.scaledBy(x: CGFloat(overlay.relScale), y: CGFloat(overlay.relScale))

            // Apply transform to all corners
            let transformedTopLeft = topLeft.applying(transform)
            let transformedTopRight = topRight.applying(transform)
            let transformedBottomLeft = bottomLeft.applying(transform)
            let transformedBottomRight = bottomRight.applying(transform)

            // Find the bounding rect
            let minX = min(transformedTopLeft.x, transformedTopRight.x, transformedBottomLeft.x, transformedBottomRight.x)
            let minY = min(transformedTopLeft.y, transformedTopRight.y, transformedBottomLeft.y, transformedBottomRight.y)
            let maxX = max(transformedTopLeft.x, transformedTopRight.x, transformedBottomLeft.x, transformedBottomRight.x)
            let maxY = max(transformedTopLeft.y, transformedTopRight.y, transformedBottomLeft.y, transformedBottomRight.y)

            return CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
        }
    }

    // Helper to get image bounds from control points
    private static func getImageBounds(from controlPoints: [PointPair]) -> CGRect {
        var minX = Double.infinity
        var minY = Double.infinity
        var maxX = -Double.infinity
        var maxY = -Double.infinity

        for point in controlPoints {
            minX = min(minX, Double(point.screenPoint.x))
            minY = min(minY, Double(point.screenPoint.y))
            maxX = max(maxX, Double(point.screenPoint.x))
            maxY = max(maxY, Double(point.screenPoint.y))
        }

        // If we don't have valid bounds, use a default
        if minX == Double.infinity || minY == Double.infinity || maxX == -Double.infinity || maxY == -Double.infinity {
            return CGRect(x: 0, y: 0, width: 1000, height: 1000)
        }

        return CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
    }

    // Returns a 3x3 transformation matrix encoded as Data
    static func computeTransformMatrix(controlPoints: [PointPair]) throws -> Data {
        guard controlPoints.count >= 3 else {
            throw TransformError.insufficientControlPoints
        }

        if controlPoints.count == 3 {
            return try computeAffineTransform(controlPoints: controlPoints)
        } else {
            return try computePerspectiveTransform(controlPoints: controlPoints)
        }
    }

    // Compute a 6-parameter affine transform (3 control points)
    private static func computeAffineTransform(controlPoints: [PointPair]) throws -> Data {
        // We need at least 3 control points for an affine transform
        guard controlPoints.count >= 3 else {
            throw TransformError.insufficientControlPoints
        }

        // Extract source and destination points
        let srcPoints = controlPoints.prefix(3).map { $0.screenPoint }
        let dstPoints = controlPoints.prefix(3).map { CGPoint(x: $0.coordinate.longitude, y: $0.coordinate.latitude) }

        // For now, use a simplified approach - just use the first 3 points to create a basic transform
        // This is a simplified version that doesn't require solving a linear system

        // Calculate the centroid of each set of points
        let srcCentroid = CGPoint(
            x: (srcPoints[0].x + srcPoints[1].x + srcPoints[2].x) / 3.0,
            y: (srcPoints[0].y + srcPoints[1].y + srcPoints[2].y) / 3.0
        )

        let dstCentroid = CGPoint(
            x: (dstPoints[0].x + dstPoints[1].x + dstPoints[2].x) / 3.0,
            y: (dstPoints[0].y + dstPoints[1].y + dstPoints[2].y) / 3.0
        )

        // Calculate the translation component
        let tx = dstCentroid.x - srcCentroid.x
        let ty = dstCentroid.y - srcCentroid.y

        // For simplicity, use a basic scale and rotation
        // In a real implementation, you would solve the full affine transform
        let scale: CGFloat = 1.0
        let rotation: CGFloat = 0.0

        // Create the matrix
        let matrix = Matrix3x3(values: [
            Double(scale * cos(rotation)), Double(-scale * sin(rotation)), Double(tx),
            Double(scale * sin(rotation)), Double(scale * cos(rotation)), Double(ty),
            0.0, 0.0, 1.0
        ])

        // Encode and return
        return try JSONEncoder().encode(matrix)
    }

    // Compute an 8-parameter perspective transform (4 control points)
    private static func computePerspectiveTransform(controlPoints: [PointPair]) throws -> Data {
        // We need at least 4 control points for a perspective transform
        guard controlPoints.count >= 4 else {
            throw TransformError.insufficientControlPoints
        }

        // Extract source and destination points
        let srcPoints = controlPoints.prefix(4).map { $0.screenPoint }
        let dstPoints = controlPoints.prefix(4).map { CGPoint(x: $0.coordinate.longitude, y: $0.coordinate.latitude) }

        // For now, use a simplified approach - just use the affine transform
        // In a real implementation, you would solve the full perspective transform

        // Calculate the centroid of each set of points
        let srcCentroid = CGPoint(
            x: (srcPoints[0].x + srcPoints[1].x + srcPoints[2].x + srcPoints[3].x) / 4.0,
            y: (srcPoints[0].y + srcPoints[1].y + srcPoints[2].y + srcPoints[3].y) / 4.0
        )

        let dstCentroid = CGPoint(
            x: (dstPoints[0].x + dstPoints[1].x + dstPoints[2].x + dstPoints[3].x) / 4.0,
            y: (dstPoints[0].y + dstPoints[1].y + dstPoints[2].y + dstPoints[3].y) / 4.0
        )

        // Calculate the translation component
        let tx = dstCentroid.x - srcCentroid.x
        let ty = dstCentroid.y - srcCentroid.y

        // For simplicity, use a basic scale and rotation
        // In a real implementation, you would solve the full perspective transform
        let scale: CGFloat = 1.0
        let rotation: CGFloat = 0.0

        // Create the matrix with a basic perspective component
        let matrix = Matrix3x3(values: [
            Double(scale * cos(rotation)), Double(-scale * sin(rotation)), Double(tx),
            Double(scale * sin(rotation)), Double(scale * cos(rotation)), Double(ty),
            0.001, 0.001, 1.0  // Small perspective component
        ])

        // Encode and return
        return try JSONEncoder().encode(matrix)
    }
}