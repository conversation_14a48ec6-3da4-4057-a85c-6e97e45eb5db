import MapKit
import UIKit

/// Custom MKOverlayRenderer for rendering image overlays
class ImageOverlayRenderer: MKOverlayRenderer {
    // The image to render
    private var image: UIImage?

    // The transformation matrix
    private var transformMatrix: Matrix3x3?

    // The overlay data
    private var overlayData: Overlay

    // MARK: - Initialization

    init(overlay: MKOverlay, overlayData: Overlay) {
        self.overlayData = overlayData

        // Initialize with the MKOverlay
        super.init(overlay: overlay)

        // Load the image
        loadImage()

        // Decode the transform matrix if available
        if let transformData = overlayData.transform {
            do {
                self.transformMatrix = try JSONDecoder().decode(Matrix3x3.self, from: transformData)
            } catch {
                print("Error decoding transform matrix: \(error)")
            }
        }
    }

    // MARK: - Image Loading

    private func loadImage() {
        // Try to load from imageLocalId (Photos asset) first
        if overlayData.imageLocalId != nil {
            // TODO: Implement Photos framework integration
            // For now, just try to load from URL
            print("Image has imageLocalId: \(String(describing: overlayData.imageLocalId)), but Photos integration not implemented yet")
        }

        // Use ImageManager to load the image and update the URL if needed
        if let loadedImage = ImageManager.shared.loadImage(from: overlayData.imageURL) {
            self.image = loadedImage

            // Make sure the overlay has the correct URL
            ImageManager.shared.updateOverlayImageURL(overlayData)
        } else {
            print("ImageOverlayRenderer: Failed to load image for overlay")
        }
    }

    // MARK: - Rendering

    override func draw(_ mapRect: MKMapRect, zoomScale: MKZoomScale, in context: CGContext) {
        guard let image = self.image else {
            print("ImageOverlayRenderer: No image available")
            return
        }

        // Calculate the drawing rect
        let mapRectRect = rect(for: mapRect)

        // Always draw the overlay regardless of intersection
        // This ensures the overlay is visible even when zooming
        // We'll skip the intersection check entirely

        // Print debug info
        print("Drawing overlay at center: \(overlayData.centerLat), \(overlayData.centerLon)")
        print("Overlay opacity: \(overlayData.opacity), rotation: \(overlayData.rotation), scale: \(overlayData.relScale)")

        // Save the context state
        context.saveGState()

        // Set the blend mode to normal
        context.setBlendMode(.normal)

        // Set the opacity
        context.setAlpha(CGFloat(overlayData.opacity))

        // Always use high quality interpolation
        context.interpolationQuality = .high

        // Get the center point of the overlay
        let center = point(for: MKMapPoint(CLLocationCoordinate2D(latitude: overlayData.centerLat, longitude: overlayData.centerLon)))

        // Translate to the center point
        context.translateBy(x: center.x, y: center.y)

        // Apply rotation
        context.rotate(by: CGFloat(overlayData.rotation))

        // Apply scale with vertical flip to fix inversion
        context.scaleBy(x: CGFloat(overlayData.relScale), y: -CGFloat(overlayData.relScale))

        // Translate back to draw the image centered
        context.translateBy(x: -image.size.width / 2, y: -image.size.height / 2)

        // Draw the image
        let imageRect = CGRect(origin: .zero, size: image.size)
        if let cgImage = image.cgImage {
            context.draw(cgImage, in: imageRect)
            print("Drew image using CGImage")
        } else {
            image.draw(in: imageRect)
            print("Drew image using UIImage.draw")
        }

        // Restore the context state
        context.restoreGState()

        // Draw a small indicator at the center point (for debugging)
        if true { // Enable center point indicator to help with debugging
            context.saveGState()
            context.setFillColor(UIColor.red.cgColor)
            context.fillEllipse(in: CGRect(x: center.x - 3, y: center.y - 3, width: 6, height: 6))

            // Draw crosshair
            context.setStrokeColor(UIColor.red.cgColor)
            context.setLineWidth(1.0)

            // Horizontal line
            context.move(to: CGPoint(x: center.x - 10, y: center.y))
            context.addLine(to: CGPoint(x: center.x + 10, y: center.y))

            // Vertical line
            context.move(to: CGPoint(x: center.x, y: center.y - 10))
            context.addLine(to: CGPoint(x: center.x, y: center.y + 10))

            context.strokePath()
            context.restoreGState()
        }
    }

    // MARK: - Utility Methods

    /// Update the overlay and refresh the renderer
    func updateOverlayData(_ overlayData: Overlay) {
        self.overlayData = overlayData

        // Update the transform matrix if available
        if let transformData = overlayData.transform {
            do {
                self.transformMatrix = try JSONDecoder().decode(Matrix3x3.self, from: transformData)
            } catch {
                print("Error decoding transform matrix: \(error)")
            }
        }

        // Trigger a redraw
        setNeedsDisplay()
    }
}
