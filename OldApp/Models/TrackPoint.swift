import CoreData
import CoreLocation

@objc(TrackPoint)
public class TrackPoint: NSManagedObject {
    @NSManaged public var lat: Double
    @NSManaged public var lon: Double
    @NSManaged public var timestamp: Date
    @NSManaged public var altitudeValue: Double
    @NSManaged public var horizontalAccuracy: Double
    @NSManaged public var segment: TrackSegment?

    // MARK: - Lifecycle

    public override func awakeFromInsert() {
        super.awakeFromInsert()

        // Set default values for required attributes
        setPrimitiveValue(Date(), forKey: "timestamp")
        setPrimitiveValue(-1000.0, forKey: "altitudeValue")
        setPrimitiveValue(0.0, forKey: "horizontalAccuracy")
        setPrimitiveValue(0.0, forKey: "lat")
        setPrimitiveValue(0.0, forKey: "lon")
    }

    // MARK: - Computed Properties

    /// Get/set the altitude with optional handling
    var altitude: Double? {
        get {
            // Use -1000 as a sentinel value for nil
            return altitudeValue > -999 ? altitudeValue : nil
        }
        set {
            altitudeValue = newValue ?? -1000
        }
    }

    /// Get the location as a CLLocationCoordinate2D
    var coordinate: CLLocationCoordinate2D {
        CLLocationCoordinate2D(latitude: lat, longitude: lon)
    }

    /// Convert to a CLLocation
    var location: CLLocation {
        CLLocation(
            coordinate: coordinate,
            altitude: altitude ?? 0,
            horizontalAccuracy: horizontalAccuracy,
            verticalAccuracy: -1,  // Unknown
            timestamp: timestamp
        )
    }
}
