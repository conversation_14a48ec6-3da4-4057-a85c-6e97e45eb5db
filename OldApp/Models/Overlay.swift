import CoreData
import CoreLocation
import UIKit
import Swift<PERSON>

@objc(Overlay)
public class Overlay: NSManagedObject {
    @NSManaged public var id: UUID
    @NSManaged public var imageLocalId: String?
    @NSManaged public var imageURL: URL?
    @NSManaged public var centerLat: Double
    @NSManaged public var centerLon: Double
    @NSManaged public var baselineFit: Double
    @NSManaged public var relScale: Double
    @NSManaged public var rotation: Double  // radians
    @NSManaged public var opacity: Double
    @NSManaged public var controlPoints: Data
    @NSManaged public var transform: Data?
    @NSManaged public var zOrder: Int32
    @NSManaged public var isVisible: Bool
    @NSManaged public var createdAt: Date
    @NSManaged public var updatedAt: Date

    // MARK: - Lifecycle

    public override func awakeFromInsert() {
        super.awakeFromInsert()

        // Set default values for required attributes
        setPrimitiveValue(UUID(), forKey: "id")
        setPrimitiveValue(Date(), forKey: "createdAt")
        setPrimitiveValue(Date(), forKey: "updatedAt")
        setPrimitiveValue(0.0, forKey: "centerLat")
        setPrimitiveValue(0.0, forKey: "centerLon")
        setPrimitiveValue(0.0, forKey: "rotation")
        setPrimitiveValue(1.0, forKey: "relScale")
        setPrimitiveValue(1.0, forKey: "opacity")
        setPrimitiveValue(0.0, forKey: "baselineFit")
        setPrimitiveValue(true, forKey: "isVisible")
        setPrimitiveValue(0, forKey: "zOrder")

        // Create an empty Data object for controlPoints
        let emptyData = "AAAAAAAAAAAAAAAA".data(using: .utf8) ?? Data()
        setPrimitiveValue(emptyData, forKey: "controlPoints")
    }

    // MARK: - Computed Properties

    /// Get the control points as an array of PointPair objects
    var decodedControlPoints: [PointPair] {
        get {
            if controlPoints.isEmpty {
                return []
            }

            do {
                return try JSONDecoder().decode([PointPair].self, from: controlPoints)
            } catch {
                print("Error decoding control points: \(error)")
                return []
            }
        }
        set {
            do {
                controlPoints = try JSONEncoder().encode(newValue)
            } catch {
                print("Error encoding control points: \(error)")
                controlPoints = Data()
            }
        }
    }

    /// Get the center coordinate
    var centerCoordinate: CLLocationCoordinate2D {
        CLLocationCoordinate2D(latitude: centerLat, longitude: centerLon)
    }

    // MARK: - Utility Methods

    /// Add a control point
    func addControlPoint(_ point: PointPair) {
        var points = decodedControlPoints
        points.append(point)
        decodedControlPoints = points

        // Update the transform if we have enough points
        if points.count >= 3 {
            updateTransform()
        }
    }

    /// Remove a control point
    func removeControlPoint(at index: Int) {
        var points = decodedControlPoints
        guard index < points.count else { return }

        points.remove(at: index)
        decodedControlPoints = points

        // Update the transform if we have enough points
        if points.count >= 3 {
            updateTransform()
        } else {
            // Not enough points for a transform
            transform = nil
        }
    }

    /// Update the transform matrix based on control points
    func updateTransform() {
        let points = decodedControlPoints
        guard points.count >= 3 else {
            transform = nil
            return
        }

        do {
            transform = try OverlayTransform.computeTransformMatrix(controlPoints: points)
        } catch {
            print("Error computing transform: \(error)")
            transform = nil
        }
    }
}

// MARK: - Fetch Request
extension Overlay {
    static func fetchRequest() -> NSFetchRequest<Overlay> {
        return NSFetchRequest<Overlay>(entityName: "Overlay")
    }
}