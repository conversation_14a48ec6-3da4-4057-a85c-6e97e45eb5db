import CoreGraphics
import CoreLocation

struct PointPair: Codable, Hashable {
    var screenPoint: CGPoint                  // in overlay-image space (pixels)
    var coordinate: CLLocationCoordinate2D    // geo anchor

    // Make CLLocationCoordinate2D Codable
    enum CodingKeys: String, CodingKey {
        case screenX, screenY, latitude, longitude
    }

    init(screenPoint: CGPoint, coordinate: CLLocationCoordinate2D) {
        self.screenPoint = screenPoint
        self.coordinate = coordinate
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let x = try container.decode(Double.self, forKey: .screenX)
        let y = try container.decode(Double.self, forKey: .screenY)
        screenPoint = CGPoint(x: x, y: y)

        let lat = try container.decode(Double.self, forKey: .latitude)
        let lon = try container.decode(Double.self, forKey: .longitude)
        coordinate = CLLocationCoordinate2D(latitude: lat, longitude: lon)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(screenPoint.x, forKey: .screenX)
        try container.encode(screenPoint.y, forKey: .screenY)
        try container.encode(coordinate.latitude, forKey: .latitude)
        try container.encode(coordinate.longitude, forKey: .longitude)
    }

    // MARK: - Hashable Implementation

    func hash(into hasher: inout Hasher) {
        hasher.combine(screenPoint.x)
        hasher.combine(screenPoint.y)
        hasher.combine(coordinate.latitude)
        hasher.combine(coordinate.longitude)
    }

    // MARK: - Equatable Implementation

    static func == (lhs: PointPair, rhs: PointPair) -> Bool {
        return lhs.screenPoint.x == rhs.screenPoint.x &&
               lhs.screenPoint.y == rhs.screenPoint.y &&
               lhs.coordinate.latitude == rhs.coordinate.latitude &&
               lhs.coordinate.longitude == rhs.coordinate.longitude
    }
}