import CoreData
import CoreLocation
import UIKit
import MapKit

@objc(TrackSegment)
public class TrackSegment: NSManagedObject {
    @NSManaged public var id: UUID
    @NSManaged public var colorHex: String
    @NSManaged public var startedAt: Date
    @NSManaged public var endedAt: Date?
    @NSManaged public var overlayId: UUID?
    @NSManaged public var points: NSSet?

    // MARK: - Lifecycle

    public override func awakeFromInsert() {
        super.awakeFromInsert()

        // Set default values for required attributes
        setPrimitiveValue(UUID(), forKey: "id")
        setPrimitiveValue("#0000FF", forKey: "colorHex")
        setPrimitiveValue(Date(), forKey: "startedAt")
    }

    // MARK: - Computed Properties

    /// Get all points sorted by timestamp
    var sortedPoints: [TrackPoint] {
        guard let points = points else { return [] }
        let pointsArray = points.allObjects as! [TrackPoint]
        return pointsArray.sorted { $0.timestamp < $1.timestamp }
    }

    /// Get the UIColor from the stored hex string
    var color: UIColor {
        get {
            UIColor(hex: colorHex) ?? .systemBlue
        }
        set {
            colorHex = newValue.toHex() ?? "#0000FF"
        }
    }

    /// Get the track as an MKPolyline for rendering
    var polyline: MKPolyline {
        let sortedPoints = self.sortedPoints
        let coordinates = sortedPoints.map { CLLocationCoordinate2D(latitude: $0.lat, longitude: $0.lon) }
        return MKPolyline(coordinates: coordinates, count: coordinates.count)
    }

    // MARK: - Utility Methods

    /// Add a new location to this track segment
    func addLocation(_ location: CLLocation) {
        let context = self.managedObjectContext!

        let point = TrackPoint(context: context)
        point.lat = location.coordinate.latitude
        point.lon = location.coordinate.longitude
        point.timestamp = location.timestamp
        point.horizontalAccuracy = location.horizontalAccuracy
        point.altitude = location.altitude
        point.segment = self

        // Update the end time
        self.endedAt = location.timestamp

        // Save the context
        do {
            try context.save()
        } catch {
            print("Error saving location: \(error)")
        }
    }

    /// Export this track segment as GPX
    func exportAsGPX() -> String {
        let dateFormatter = ISO8601DateFormatter()

        var gpx = """
        <?xml version="1.0" encoding="UTF-8"?>
        <gpx version="1.1" creator="FogOfWarV2" xmlns="http://www.topografix.com/GPX/1/1">
          <trk>
            <name>Track \(dateFormatter.string(from: startedAt))</name>
            <trkseg>
        """

        for point in sortedPoints {
            gpx += """
                <trkpt lat="\(point.lat)" lon="\(point.lon)">
                  <ele>\(point.altitude ?? 0)</ele>
                  <time>\(dateFormatter.string(from: point.timestamp))</time>
                </trkpt>
            """
        }

        gpx += """
            </trkseg>
          </trk>
        </gpx>
        """

        return gpx
    }
}

// MARK: - UIColor Hex Extensions

extension UIColor {
    convenience init?(hex: String) {
        let r, g, b, a: CGFloat

        if hex.hasPrefix("#") {
            let start = hex.index(hex.startIndex, offsetBy: 1)
            let hexColor = String(hex[start...])

            if hexColor.count == 6 {
                let scanner = Scanner(string: hexColor)
                var hexNumber: UInt64 = 0

                if scanner.scanHexInt64(&hexNumber) {
                    r = CGFloat((hexNumber & 0xff0000) >> 16) / 255
                    g = CGFloat((hexNumber & 0x00ff00) >> 8) / 255
                    b = CGFloat(hexNumber & 0x0000ff) / 255
                    a = 1.0

                    self.init(red: r, green: g, blue: b, alpha: a)
                    return
                }
            }
        }

        return nil
    }

    func toHex() -> String? {
        guard let components = cgColor.components, components.count >= 3 else {
            return nil
        }

        let r = Float(components[0])
        let g = Float(components[1])
        let b = Float(components[2])

        return String(format: "#%02lX%02lX%02lX", lroundf(r * 255), lroundf(g * 255), lroundf(b * 255))
    }
}
