import SwiftUI
import MapKit
import CoreLocation

struct MapViewWithOverlay: UIViewRepresentable {
    @Binding var region: MKCoordinateRegion
    let overlay: Overlay
    let workingState: WorkingOverlayState

    // Always center the image
    @Binding var centerImageMode: Bool

    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView()
        mapView.delegate = context.coordinator

        // Configure map settings
        mapView.showsUserLocation = true
        mapView.showsCompass = true
        mapView.showsScale = true

        // Set the map type to standard (fix for black map)
        mapView.mapType = .standard

        // Force the map to load tiles by setting a valid region immediately
        let initialRegion = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 47.073286, longitude: -80.102979),
            span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
        )
        mapView.region = initialRegion // Use direct assignment first
        mapView.setRegion(initialRegion, animated: false) // Then use setRegion

        // Print debug info
        print("MapViewWithOverlay - Initial map center: \(initialRegion.center.latitude), \(initialRegion.center.longitude)")
        print("MapViewWithOverlay - Initial map span: \(initialRegion.span.latitudeDelta), \(initialRegion.span.longitudeDelta)")

        // Enable zooming and panning
        mapView.isZoomEnabled = true
        mapView.isScrollEnabled = true
        mapView.isPitchEnabled = false // Disable 3D pitch to keep a flat map

        // Set camera boundary with more reasonable values
        let zoomRange = MKMapView.CameraZoomRange(
            minCenterCoordinateDistance: 50, // 50 meters minimum
            maxCenterCoordinateDistance: 500000 // 500 km maximum
        )
        mapView.setCameraZoomRange(zoomRange, animated: false)

        // Force the map to render
        mapView.setNeedsDisplay()

        // Set up notification observer for overlay updates
        NotificationCenter.default.removeObserver(context.coordinator, name: NSNotification.Name("UpdateOverlay"), object: nil)
        NotificationCenter.default.addObserver(context.coordinator, selector: #selector(context.coordinator.updateOverlayFromNotification(_:)), name: NSNotification.Name("UpdateOverlay"), object: nil)

        return mapView
    }

    func updateUIView(_ mapView: MKMapView, context: Context) {
        // Update the coordinator's centerImageMode
        context.coordinator.centerImageMode = centerImageMode

        // Store the current map center coordinate
        context.coordinator.mapCenterCoordinate = region.center

        // Print debug info
        print("MapViewWithOverlay updateUIView - region center: \(region.center.latitude), \(region.center.longitude)")
        print("MapViewWithOverlay updateUIView - map center: \(mapView.region.center.latitude), \(mapView.region.center.longitude)")

        // Always update the region to ensure the map is properly positioned
        mapView.setRegion(region, animated: false)

        // In center image mode, the image stays at the center of the map
        if centerImageMode {
            // Store the values to update after the view update cycle
            let newLat = region.center.latitude
            let newLon = region.center.longitude

            // Update center immediately
            self.workingState.updateCenter(latitude: newLat, longitude: newLon)
        }

        // Always update overlay when any parameter changes
        updateMapOverlay(mapView, context: context)

        // Force a redraw
        mapView.setNeedsDisplay()
    }

    // Helper to determine if we should update the region
    private func shouldUpdateRegion(_ currentRegion: MKCoordinateRegion, newRegion: MKCoordinateRegion) -> Bool {
        // Check if the center coordinates have changed
        let centerDelta = CLLocation(latitude: currentRegion.center.latitude, longitude: currentRegion.center.longitude)
            .distance(from: CLLocation(latitude: newRegion.center.latitude, longitude: newRegion.center.longitude))

        // Check if the span has changed
        let spanChanged = abs(currentRegion.span.latitudeDelta - newRegion.span.latitudeDelta) > 0.0001 ||
                          abs(currentRegion.span.longitudeDelta - newRegion.span.longitudeDelta) > 0.0001

        // Update if center has moved more than 10 meters or span has changed
        // Using a smaller threshold to be more responsive to changes
        return centerDelta > 10 || spanChanged
    }

    private func updateMapOverlay(_ mapView: MKMapView, context: Context) {
        // This method is called during view updates, so we need to be careful
        // not to trigger state changes that would cause SwiftUI to update again

        // Remove all existing overlays
        let existingOverlays = mapView.overlays.compactMap { $0 as? ImageOverlay }
        mapView.removeOverlays(existingOverlays)

        // Create a working copy of the overlay with the current working state
        let workingOverlay = overlay

        // Apply the current working state to the overlay
        workingOverlay.opacity = workingState.opacity
        workingOverlay.rotation = workingState.rotation
        workingOverlay.relScale = workingState.scale

        // Make sure we're using the latest center coordinates from the working state
        workingOverlay.centerLat = workingState.centerLat
        workingOverlay.centerLon = workingState.centerLon

        // Create a basic transform matrix based on the current parameters
        var matrix = Matrix3x3()

        // Apply scale (use positive scale for matrix, the renderer will handle the vertical flip)
        matrix = Matrix3x3.scaling(sx: workingState.scale, sy: workingState.scale) * matrix

        // Apply rotation
        matrix = Matrix3x3.rotation(angle: workingState.rotation) * matrix

        // Encode the matrix - handle potential errors
        do {
            workingOverlay.transform = try JSONEncoder().encode(matrix)
        } catch {
            print("Error encoding matrix: \(error)")
        }

        // Add the overlay to the map
        let imageOverlay = ImageOverlay(overlayData: workingOverlay)
        mapView.addOverlay(imageOverlay, level: .aboveRoads)

        // Update the bounds
        let frame = OverlayTransform.computeOverlayFrame(overlay: workingOverlay, in: mapView)
        imageOverlay.updateBounds(with: frame, in: mapView)
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self, centerImageMode: centerImageMode)
    }

    class Coordinator: NSObject, MKMapViewDelegate {
        var parent: MapViewWithOverlay
        private var renderers = [ObjectIdentifier: MKOverlayRenderer]()
        var centerImageMode: Bool
        var centerPoint: CGPoint?
        var mapCenterCoordinate: CLLocationCoordinate2D?

        init(_ parent: MapViewWithOverlay, centerImageMode: Bool) {
            self.parent = parent
            self.centerImageMode = centerImageMode
            super.init()
        }

        deinit {
            // Remove notification observer
            NotificationCenter.default.removeObserver(self)
        }

        // Provide renderers for overlays
        func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
            // If we already have a renderer for this overlay, return it
            let id = ObjectIdentifier(overlay)
            if let renderer = renderers[id] {
                return renderer
            }

            // Create a new renderer based on the overlay type
            if let imageOverlay = overlay as? ImageOverlay {
                let id = ObjectIdentifier(overlay)
                let renderer = ImageOverlayRenderer(overlay: overlay, overlayData: imageOverlay.overlayData)
                renderers[id] = renderer
                return renderer
            }

            // Default renderer
            return MKOverlayRenderer(overlay: overlay)
        }

        // Handle region changes
        func mapView(_ mapView: MKMapView, regionDidChangeAnimated animated: Bool) {
            parent.region = mapView.region

            // Update the map center coordinate
            self.mapCenterCoordinate = mapView.region.center

            // In center image mode, the image stays at the center of the map
            if centerImageMode {
                // Store the values to update after the view update cycle
                let newLat = mapView.region.center.latitude
                let newLon = mapView.region.center.longitude

                // Use DispatchQueue to avoid publishing during view updates
                DispatchQueue.main.async {
                    self.parent.workingState.updateCenter(latitude: newLat, longitude: newLon)
                    self.updateMapOverlay(mapView)
                }
            }
        }

        // Called when the overlay is about to be drawn
        func mapView(_ mapView: MKMapView, willRender renderer: MKOverlayRenderer) {
            // Force update of the overlay when rendering is about to happen
            if let imageRenderer = renderer as? ImageOverlayRenderer {
                imageRenderer.setNeedsDisplay()
            }
        }

        // Handle notification to update overlay
        @objc func updateOverlayFromNotification(_ notification: Notification) {
            // Use a simpler approach - just post a notification to update the map
            DispatchQueue.main.async {
                // Find the map view directly from the notification's userInfo if available
                if let mapView = notification.object as? MKMapView {
                    self.updateMapOverlay(mapView)
                    mapView.setNeedsDisplay()
                } else {
                    // Search for the map view in the window hierarchy
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let keyWindow = windowScene.windows.first {

                        // Find all MKMapViews in the window
                        let mapViews = self.findAllMapViews(in: keyWindow)

                        // Use the first map view found
                        if let mapView = mapViews.first {
                            self.updateMapOverlay(mapView)
                            mapView.setNeedsDisplay()
                        }
                    }
                }
            }
        }

        // Helper to find all MKMapViews in a view hierarchy
        private func findAllMapViews(in view: UIView) -> [MKMapView] {
            var mapViews = [MKMapView]()

            // Check if this view is an MKMapView
            if let mapView = view as? MKMapView {
                mapViews.append(mapView)
            }

            // Check all subviews
            for subview in view.subviews {
                mapViews.append(contentsOf: findAllMapViews(in: subview))
            }

            return mapViews
        }

        // Helper to update the map overlay
        private func updateMapOverlay(_ mapView: MKMapView) {
            // This method should be called from a DispatchQueue.main.async block
            // to avoid publishing during view updates

            // Remove all existing overlays
            let existingOverlays = mapView.overlays.compactMap { $0 as? ImageOverlay }
            mapView.removeOverlays(existingOverlays)

            // Create a working copy of the overlay with the current working state
            let workingOverlay = parent.overlay

            // Apply the current working state to the overlay
            workingOverlay.opacity = parent.workingState.opacity
            workingOverlay.rotation = parent.workingState.rotation
            workingOverlay.relScale = parent.workingState.scale
            workingOverlay.centerLat = parent.workingState.centerLat
            workingOverlay.centerLon = parent.workingState.centerLon

            // Create a basic transform matrix based on the current parameters
            var matrix = Matrix3x3()

            // Apply scale (use positive scale for matrix, the renderer will handle the vertical flip)
            matrix = Matrix3x3.scaling(sx: parent.workingState.scale, sy: parent.workingState.scale) * matrix

            // Apply rotation
            matrix = Matrix3x3.rotation(angle: parent.workingState.rotation) * matrix

            // Encode the matrix - handle potential errors
            do {
                workingOverlay.transform = try JSONEncoder().encode(matrix)
            } catch {
                print("Error encoding matrix: \(error)")
            }

            // Add the overlay to the map
            let imageOverlay = ImageOverlay(overlayData: workingOverlay)
            mapView.addOverlay(imageOverlay, level: .aboveRoads)

            // Update the bounds
            let frame = OverlayTransform.computeOverlayFrame(overlay: workingOverlay, in: mapView)
            imageOverlay.updateBounds(with: frame, in: mapView)
        }
    }
}
