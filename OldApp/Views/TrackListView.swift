import SwiftUI
import CoreData
import MapKit
import UIKit

struct TrackListView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss

    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \TrackSegment.startedAt, ascending: false)],
        animation: .default)
    private var tracks: FetchedResults<TrackSegment>

    @State private var showingExportSheet = false
    @State private var exportURL: URL?
    @State private var selectedTracks = Set<UUID>()
    @State private var isSelecting = false

    var body: some View {
        List {
            ForEach(tracks, id: \.id) { track in
                NavigationLink(destination: TrackEditView(track: track)) {
                    TrackRow(track: track, isSelected: selectedTracks.contains(track.id), isSelecting: isSelecting)
                        .contentShape(Rectangle())
                        .onTapGesture {
                            if isSelecting {
                                toggleTrackSelection(track)
                            }
                        }
                }
                .disabled(isSelecting)
            }
            .onDelete(perform: deleteTracks)
        }
        .navigationTitle("GPS Tracks")
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(isSelecting ? "Done" : "Select") {
                    isSelecting.toggle()
                    if !isSelecting {
                        selectedTracks.removeAll()
                    }
                }
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                Menu {
                    Button(action: {
                        exportSelectedTracks()
                    }) {
                        Label("Export Selected", systemImage: "square.and.arrow.up")
                    }
                    .disabled(selectedTracks.isEmpty)

                    Button(action: {
                        deleteSelectedTracks()
                    }) {
                        Label("Delete Selected", systemImage: "trash")
                    }
                    .disabled(selectedTracks.isEmpty)

                    Divider()

                    Button(action: {
                        exportAllTracks()
                    }) {
                        Label("Export All", systemImage: "square.and.arrow.up.on.square")
                    }
                    .disabled(tracks.isEmpty)
                } label: {
                    Image(systemName: "ellipsis.circle")
                }
            }

            ToolbarItem(placement: .navigationBarLeading) {
                Button("Done") {
                    dismiss()
                }
            }
        }
        .sheet(isPresented: $showingExportSheet, onDismiss: {
            exportURL = nil
        }) {
            if let url = exportURL {
                ShareSheet(items: [url])
            }
        }
    }

    private func toggleTrackSelection(_ track: TrackSegment) {
        if selectedTracks.contains(track.id) {
            selectedTracks.remove(track.id)
        } else {
            selectedTracks.insert(track.id)
        }
    }

    private func deleteTracks(at offsets: IndexSet) {
        withAnimation {
            offsets.map { tracks[$0] }.forEach(viewContext.delete)
            saveContext()
        }
    }

    private func deleteSelectedTracks() {
        withAnimation {
            for track in tracks {
                if selectedTracks.contains(track.id) {
                    viewContext.delete(track)
                }
            }
            saveContext()
            selectedTracks.removeAll()
            isSelecting = false
        }
    }

    private func exportSelectedTracks() {
        let tracksToExport = tracks.filter { selectedTracks.contains($0.id) }
        exportTracks(Array(tracksToExport))
    }

    private func exportAllTracks() {
        exportTracks(Array(tracks))
    }

    private func exportTracks(_ tracksToExport: [TrackSegment]) {
        guard !tracksToExport.isEmpty else { return }

        // Generate GPX data
        let gpxData = TrackManager.shared.exportMultipleTracksAsGPX(tracksToExport)

        // Create a filename with the current date
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd_HHmmss"
        let dateString = dateFormatter.string(from: Date())
        let fileName = "Tracks_\(dateString).gpx"

        // Save to file
        if let url = TrackManager.shared.saveGPXToFile(gpxData, fileName: fileName) {
            exportURL = url
            showingExportSheet = true
        }
    }

    private func saveContext() {
        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            print("Error saving context: \(nsError), \(nsError.userInfo)")
        }
    }
}

struct TrackRow: View {
    let track: TrackSegment
    let isSelected: Bool
    let isSelecting: Bool

    var body: some View {
        HStack {
            if isSelecting {
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .blue : .gray)
                    .imageScale(.large)
            }

            VStack(alignment: .leading) {
                Text(trackTitle)
                    .font(.headline)

                Text("\(track.sortedPoints.count) points • \(trackDuration)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()

            Circle()
                .fill(Color(track.color))
                .frame(width: 16, height: 16)
        }
    }

    private var trackTitle: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return "Track \(formatter.string(from: track.startedAt))"
    }

    private var trackDuration: String {
        guard let endDate = track.endedAt else { return "In progress" }

        let duration = endDate.timeIntervalSince(track.startedAt)

        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60
        let seconds = Int(duration) % 60

        if hours > 0 {
            return String(format: "%dh %dm %ds", hours, minutes, seconds)
        } else if minutes > 0 {
            return String(format: "%dm %ds", minutes, seconds)
        } else {
            return String(format: "%ds", seconds)
        }
    }
}
