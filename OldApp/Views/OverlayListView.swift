import SwiftUI
import CoreData
import PhotosUI
import UniformTypeIdentifiers

struct OverlayListView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var overlayPublisher: OverlayPublisher

    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Overlay.zOrder, ascending: true)],
        animation: .default)
    private var overlays: FetchedResults<Overlay>

    @Binding var editingOverlay: Overlay?
    @Binding var isEditorMode: Bool

    @State private var showingImagePicker = false

    var body: some View {
        List {
            ForEach(overlays, id: \.id) { overlay in
                HStack {
                    // Visibility toggle
                    Button(action: {
                        toggleVisibility(overlay)
                    }) {
                        Image(systemName: overlay.isVisible ? "eye.fill" : "eye.slash.fill")
                            .foregroundColor(overlay.isVisible ? .blue : .gray)
                    }
                    .buttonStyle(BorderlessButtonStyle())

                    // Overlay name/info
                    VStack(alignment: .leading) {
                        Text("Overlay \(overlay.createdAt, formatter: itemFormatter)")
                            .font(.headline)
                        Text("Control points: \(overlay.decodedControlPoints.count)")
                            .font(.caption)
                    }

                    Spacer()

                    // Edit button
                    Button(action: {
                        editOverlay(overlay)
                    }) {
                        Image(systemName: "pencil")
                            .foregroundColor(.blue)
                    }
                    .buttonStyle(BorderlessButtonStyle())
                }
            }
            .onDelete(perform: deleteOverlays)
            .onMove(perform: moveOverlays)
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                EditButton()
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    showingImagePicker = true
                }) {
                    Image(systemName: "plus")
                }
            }
            ToolbarItem(placement: .navigationBarLeading) {
                Button("Done") {
                    dismiss()
                }
            }
        }
        .sheet(isPresented: $showingImagePicker) {
            ImagePicker(onImageSelected: { image, url in
                addOverlay(image: image, url: url)
            })
        }
    }

    private func toggleVisibility(_ overlay: Overlay) {
        overlay.isVisible.toggle()
        saveContext()
        // Notify that the overlay has changed
        overlayPublisher.overlayDidChange()
    }

    private func editOverlay(_ overlay: Overlay) {
        editingOverlay = overlay
        isEditorMode = true
        // Don't dismiss, let the navigation happen
    }

    private func deleteOverlays(offsets: IndexSet) {
        withAnimation {
            // Get the overlays to delete
            let overlaysToDelete = offsets.map { overlays[$0] }

            // Delete them from Core Data
            overlaysToDelete.forEach(viewContext.delete)
            saveContext()

            // Clean up orphaned images
            // First get all remaining overlays
            let fetchRequest: NSFetchRequest<Overlay> = Overlay.fetchRequest()
            if let remainingOverlays = try? viewContext.fetch(fetchRequest) {
                // Clean up any orphaned images
                ImageManager.shared.cleanupOrphanedImages(overlays: remainingOverlays)
            }

            // Notify that overlays have changed
            overlayPublisher.overlayDidChange()
        }
    }

    private func moveOverlays(from source: IndexSet, to destination: Int) {
        // Update zOrder to reflect the new order
        var items = overlays.map { $0 }
        items.move(fromOffsets: source, toOffset: destination)

        // Update zOrder values
        for (index, overlay) in items.enumerated() {
            overlay.zOrder = Int32(index)
        }

        saveContext()
        // Notify that overlays have changed
        overlayPublisher.overlayDidChange()
    }

    private func addOverlay(image: UIImage, url: URL?) {
        withAnimation {
            let newOverlay = Overlay(context: viewContext)
            newOverlay.id = UUID()
            newOverlay.createdAt = Date()
            newOverlay.updatedAt = Date()
            newOverlay.isVisible = true
            newOverlay.opacity = 1.0
            newOverlay.relScale = 1.0
            newOverlay.rotation = 0.0
            newOverlay.zOrder = Int32(overlays.count)

            // Set the image URL
            if let url = url {
                newOverlay.imageURL = url
            }

            // Set initial center to the user's current location if available
            if let location = LocationManager.shared.location {
                newOverlay.centerLat = location.coordinate.latitude
                newOverlay.centerLon = location.coordinate.longitude
            } else {
                // Default location if user location is not available
                newOverlay.centerLat = 47.073286
                newOverlay.centerLon = -80.102979
            }

            // Initialize empty control points
            newOverlay.decodedControlPoints = []

            saveContext()

            // Notify that a new overlay has been added
            overlayPublisher.overlayDidChange()

            // Set this as the editing overlay
            editingOverlay = newOverlay
            isEditorMode = true
            // Don't dismiss, let the navigation happen
        }
    }

    private func saveContext() {
        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            print("Error saving context: \(nsError), \(nsError.userInfo)")
        }
    }
}

// MARK: - Formatters

private let itemFormatter: DateFormatter = {
    let formatter = DateFormatter()
    formatter.dateStyle = .short
    formatter.timeStyle = .medium
    return formatter
}()
