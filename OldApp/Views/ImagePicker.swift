import SwiftUI
import PhotosUI
import UniformTypeIdentifiers

struct ImagePicker: UIViewControllerRepresentable {
    var onImageSelected: (UIImage, URL?) -> Void

    func makeUIViewController(context: Context) -> PHPickerViewController {
        var config = PHPickerConfiguration()
        config.filter = .images
        config.selectionLimit = 1

        let picker = PHPickerViewController(configuration: config)
        picker.delegate = context.coordinator
        return picker
    }

    func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, PHPickerViewControllerDelegate {
        let parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
            picker.dismiss(animated: true)

            guard let result = results.first else { return }

            // Load the image
            result.itemProvider.loadObject(ofClass: UIImage.self) { [weak self] (object, error) in
                if let image = object as? UIImage {
                    // Ensure the image is in .up orientation
                    let correctedImage: UIImage
                    if image.imageOrientation != .up {
                        UIGraphicsBeginImageContextWithOptions(image.size, false, image.scale)
                        image.draw(in: CGRect(origin: .zero, size: image.size))
                        correctedImage = UIGraphicsGetImageFromCurrentImageContext() ?? image
                        UIGraphicsEndImageContext()
                    } else {
                        correctedImage = image
                    }

                    // Use our ImageManager to save the image
                    DispatchQueue.main.async {
                        if let savedURL = ImageManager.shared.saveImage(correctedImage) {
                            self?.parent.onImageSelected(correctedImage, savedURL)
                        } else {
                            print("Failed to save image using ImageManager")
                            self?.parent.onImageSelected(correctedImage, nil)
                        }
                    }
                }
            }
        }
    }
}
