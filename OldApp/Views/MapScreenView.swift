import SwiftUI
import MapKit
import CoreData
import CoreLocation

struct MapScreenView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject private var overlayPublisher: OverlayPublisher

    // Fetch visible overlays
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Overlay.zOrder, ascending: true)],
        predicate: NSPredicate(format: "isVisible == YES"),
        animation: .default)
    private var visibleOverlays: FetchedResults<Overlay>

    // Fetch all track segments
    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \TrackSegment.startedAt, ascending: false)],
        animation: .default)
    private var trackSegments: FetchedResults<TrackSegment>

    // State variables
    @State private var region = MKCoordinateRegion(
        center: CLLocationCoordinate2D(latitude: 47.073286, longitude: -80.102979),
        span: MKCoordinateSpan(latitudeDelta: 0.02, longitudeDelta: 0.02)
    )
    @State private var isRecording = false

    // Location manager for tracking
    @StateObject private var locationManager = LocationManager.shared

    var body: some View {
        ZStack {
            // Map view
            MapViewRepresentable(
                region: $region,
                overlays: Array(visibleOverlays),
                trackSegments: Array(trackSegments)
            )
            .ignoresSafeArea()
            .onChange(of: overlayPublisher.overlayUpdated) { oldValue, newValue in
                // Force refresh when overlays are updated
                // The view will automatically update
            }
        }
        .navigationTitle("Map")
        .toolbar {
            // Locate me button
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    locationManager.requestLocation()
                    if let location = locationManager.location {
                        region.center = location.coordinate
                    }
                }) {
                    Image(systemName: "location")
                }
            }

            // Record button
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    toggleRecording()
                }) {
                    Image(systemName: isRecording ? "stop.circle.fill" : "record.circle")
                        .foregroundColor(isRecording ? .red : .primary)
                }
            }
        }
    }

    // Toggle GPS recording
    private func toggleRecording() {
        if isRecording {
            // Stop recording
            locationManager.stopTracking()
            isRecording = false

            // End the current track segment
            if let currentSegment = locationManager.currentTrackSegment {
                currentSegment.endedAt = Date()
                try? viewContext.save()
            }
        } else {
            // Start recording
            // Create a new track segment
            let newSegment = TrackSegment(context: viewContext)
            newSegment.id = UUID()
            newSegment.startedAt = Date()
            newSegment.colorHex = getNextTrackColor()

            try? viewContext.save()

            // Start location tracking
            locationManager.startTracking(with: newSegment)
            isRecording = true
        }
    }

    // Get the next color for a track from a predefined palette
    private func getNextTrackColor() -> String {
        let colors = [
            UIColor.systemRed,
            UIColor.systemBlue,
            UIColor.systemGreen,
            UIColor.systemOrange,
            UIColor.systemPurple,
            UIColor.systemTeal
        ]

        let index = trackSegments.count % colors.count
        return colors[index].toHex() ?? "#FF0000"
    }
}

// UIViewRepresentable for MapKit
struct MapViewRepresentable: UIViewRepresentable {
    @Binding var region: MKCoordinateRegion
    let overlays: [Overlay]
    let trackSegments: [TrackSegment]

    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView()
        mapView.delegate = context.coordinator

        // Configure map settings
        mapView.showsUserLocation = true
        mapView.showsCompass = true
        mapView.showsScale = true

        // Set the map type to standard (fix for black map)
        mapView.mapType = .standard

        // Force the map to load tiles by setting a valid region immediately
        let initialRegion = MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: 47.073286, longitude: -80.102979),
            span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
        )
        mapView.region = initialRegion // Use direct assignment first
        mapView.setRegion(initialRegion, animated: false) // Then use setRegion

        // Print debug info
        print("MapScreenView - Initial map center: \(initialRegion.center.latitude), \(initialRegion.center.longitude)")
        print("MapScreenView - Initial map span: \(initialRegion.span.latitudeDelta), \(initialRegion.span.longitudeDelta)")

        // Enable zooming and panning
        mapView.isZoomEnabled = true
        mapView.isScrollEnabled = true
        mapView.isPitchEnabled = false // Disable 3D pitch to keep a flat map

        // Set camera boundary with more reasonable values
        let zoomRange = MKMapView.CameraZoomRange(
            minCenterCoordinateDistance: 50, // 50 meters minimum
            maxCenterCoordinateDistance: 500000 // 500 km maximum
        )
        mapView.setCameraZoomRange(zoomRange, animated: false)

        // Force the map to render
        mapView.setNeedsDisplay()

        return mapView
    }

    func updateUIView(_ mapView: MKMapView, context: Context) {
        // Update region if it has changed significantly
        if shouldUpdateRegion(mapView.region, newRegion: region) {
            mapView.setRegion(region, animated: true)
        }

        // Update overlays
        updateMapOverlays(mapView, context: context)

        // Update tracks
        updateMapTracks(mapView, context: context)
    }

    // Helper to determine if we should update the region
    private func shouldUpdateRegion(_ currentRegion: MKCoordinateRegion, newRegion: MKCoordinateRegion) -> Bool {
        // Check if the center coordinates have changed significantly
        let centerDelta = CLLocation(latitude: currentRegion.center.latitude, longitude: currentRegion.center.longitude)
            .distance(from: CLLocation(latitude: newRegion.center.latitude, longitude: newRegion.center.longitude))

        // Check if the span has changed significantly
        let spanChanged = abs(currentRegion.span.latitudeDelta - newRegion.span.latitudeDelta) > 0.01 ||
                          abs(currentRegion.span.longitudeDelta - newRegion.span.longitudeDelta) > 0.01

        // Update if center has moved more than 100 meters or span has changed significantly
        return centerDelta > 100 || spanChanged
    }

    private func updateMapOverlays(_ mapView: MKMapView, context: Context) {
        // Remove all existing overlays
        let existingOverlays = mapView.overlays.compactMap { $0 as? ImageOverlay }
        mapView.removeOverlays(existingOverlays)

        // Add all visible overlays
        for overlay in overlays {
            let imageOverlay = ImageOverlay(overlayData: overlay)
            mapView.addOverlay(imageOverlay, level: .aboveRoads)

            // Update the bounds
            let frame = OverlayTransform.computeOverlayFrame(overlay: overlay, in: mapView)
            imageOverlay.updateBounds(with: frame, in: mapView)
        }

        // Force a redraw
        mapView.setNeedsDisplay()
    }

    private func updateMapTracks(_ mapView: MKMapView, context: Context) {
        // Remove all existing tracks
        let existingTracks = mapView.overlays.compactMap { $0 as? MKPolyline }
        mapView.removeOverlays(existingTracks)

        // Add all track segments
        for segment in trackSegments {
            let polyline = segment.polyline
            mapView.addOverlay(polyline, level: .aboveRoads)
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, MKMapViewDelegate {
        var parent: MapViewRepresentable
        private var renderers = [ObjectIdentifier: MKOverlayRenderer]()

        init(_ parent: MapViewRepresentable) {
            self.parent = parent
        }

        // Provide renderers for overlays
        func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
            // If we already have a renderer for this overlay, return it
            let id = ObjectIdentifier(overlay)
            if let renderer = renderers[id] {
                return renderer
            }

            // Create a new renderer based on the overlay type
            if let imageOverlay = overlay as? ImageOverlay {
                let id = ObjectIdentifier(overlay)
                let renderer = ImageOverlayRenderer(overlay: overlay, overlayData: imageOverlay.overlayData)
                renderers[id] = renderer
                return renderer
            } else if let polyline = overlay as? MKPolyline {
                // Find the track segment for this polyline
                let segment = parent.trackSegments.first { $0.polyline === polyline }

                let renderer = MKPolylineRenderer(polyline: polyline)
                renderer.lineWidth = 3.0

                // Use the segment color if available, otherwise default to blue
                if let segment = segment {
                    renderer.strokeColor = segment.color
                } else {
                    renderer.strokeColor = .systemBlue
                }

                renderers[id] = renderer
                return renderer
            }

            // Default renderer
            return MKOverlayRenderer(overlay: overlay)
        }

        // Called when the overlay is about to be drawn
        func mapView(_ mapView: MKMapView, willRender renderer: MKOverlayRenderer) {
            // Force update of the overlay when rendering is about to happen
            if let imageRenderer = renderer as? ImageOverlayRenderer {
                imageRenderer.setNeedsDisplay()
            }
        }

        // Handle region changes
        func mapView(_ mapView: MKMapView, regionDidChangeAnimated animated: Bool) {
            parent.region = mapView.region
        }
    }
}
