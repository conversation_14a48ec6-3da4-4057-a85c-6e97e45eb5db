import SwiftUI
import CoreData
import MapKit
import CoreLocation
import UIKit

struct TrackEditView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss

    let track: TrackSegment

    @State private var showingDeleteAlert = false
    @State private var showingExportSheet = false
    @State private var exportURL: URL?
    @State private var showingTrimSheet = false
    @State private var trimStartDate: Date
    @State private var trimEndDate: Date
    @State private var selectedColor: Color
    @State private var region: MKCoordinateRegion

    init(track: TrackSegment) {
        self.track = track

        // Initialize state variables
        let points = track.sortedPoints
        let startDate = track.startedAt
        let endDate = track.endedAt ?? Date()

        // Initialize trim dates
        _trimStartDate = State(initialValue: startDate)
        _trimEndDate = State(initialValue: endDate)

        // Initialize color
        _selectedColor = State(initialValue: Color(UIColor(hex: track.colorHex) ?? .blue))

        // Initialize map region
        if let midPoint = points.count > 0 ? points[points.count / 2] : nil {
            _region = State(initialValue: MKCoordinateRegion(
                center: CLLocationCoordinate2D(latitude: midPoint.lat, longitude: midPoint.lon),
                span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
            ))
        } else {
            // Default region if no points
            _region = State(initialValue: MKCoordinateRegion(
                center: CLLocationCoordinate2D(latitude: 47.073286, longitude: -80.102979),
                span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
            ))
        }
    }

    var body: some View {
        VStack {
            // Map view showing the track
            TrackMapView(track: track, region: $region)
                .frame(height: 300)
                .cornerRadius(12)
                .padding()

            // Track details
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Text("Start:")
                        .fontWeight(.bold)
                    Text("\(track.startedAt, formatter: itemFormatter)")
                }

                HStack {
                    Text("End:")
                        .fontWeight(.bold)
                    Text("\(track.endedAt ?? Date(), formatter: itemFormatter)")
                }

                HStack {
                    Text("Points:")
                        .fontWeight(.bold)
                    Text("\(track.sortedPoints.count)")
                }

                HStack {
                    Text("Color:")
                        .fontWeight(.bold)

                    ColorPicker("", selection: $selectedColor)
                        .onChange(of: selectedColor) { oldValue, newValue in
                            updateTrackColor(newValue)
                        }
                }
            }
            .padding()

            Divider()

            // Action buttons
            VStack(spacing: 16) {
                Button(action: {
                    showingTrimSheet = true
                }) {
                    Label("Trim Track", systemImage: "scissors")
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.bordered)

                Button(action: {
                    exportTrack()
                }) {
                    Label("Export as GPX", systemImage: "square.and.arrow.up")
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.bordered)

                Button(action: {
                    showingDeleteAlert = true
                }) {
                    Label("Delete Track", systemImage: "trash")
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.bordered)
                .foregroundColor(.red)
            }
            .padding()
        }
        .navigationTitle("Edit Track")
        .navigationBarTitleDisplayMode(.inline)
        .alert("Delete Track", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                deleteTrack()
            }
        } message: {
            Text("Are you sure you want to delete this track? This action cannot be undone.")
        }
        .sheet(isPresented: $showingTrimSheet) {
            TrimTrackView(
                track: track,
                startDate: $trimStartDate,
                endDate: $trimEndDate,
                onTrim: { start, end in
                    trimTrack(from: start, to: end)
                }
            )
        }
        .sheet(isPresented: $showingExportSheet, onDismiss: {
            exportURL = nil
        }) {
            if let url = exportURL {
                ShareSheet(items: [url])
            }
        }
    }

    private func updateTrackColor(_ color: Color) {
        // Convert SwiftUI Color to UIColor
        let uiColor = UIColor(color)

        // Update the track color
        track.colorHex = uiColor.toHex() ?? "#0000FF"

        // Save the context
        do {
            try viewContext.save()
        } catch {
            print("Error saving track color: \(error)")
        }
    }

    private func trimTrack(from startDate: Date, to endDate: Date) {
        // Use TrackManager to trim the track
        if let _ = TrackManager.shared.trimTrack(track, from: startDate, to: endDate, context: viewContext) {
            // Successfully trimmed, dismiss the view
            dismiss()
        }
    }

    private func exportTrack() {
        // Generate GPX data
        let gpxData = TrackManager.shared.exportAsGPX(track)

        // Create a filename with the track start date
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd_HHmmss"
        let dateString = dateFormatter.string(from: track.startedAt)
        let fileName = "Track_\(dateString).gpx"

        // Save to file
        if let url = TrackManager.shared.saveGPXToFile(gpxData, fileName: fileName) {
            exportURL = url
            showingExportSheet = true
        }
    }

    private func deleteTrack() {
        viewContext.delete(track)

        do {
            try viewContext.save()
            dismiss()
        } catch {
            print("Error deleting track: \(error)")
        }
    }
}

// MARK: - Helper Views

struct TrackMapView: UIViewRepresentable {
    let track: TrackSegment
    @Binding var region: MKCoordinateRegion

    func makeUIView(context: Context) -> MKMapView {
        let mapView = MKMapView()
        mapView.delegate = context.coordinator

        // Configure map
        mapView.showsUserLocation = true
        mapView.showsCompass = true
        mapView.showsScale = true

        // Add the track
        let polyline = track.polyline
        mapView.addOverlay(polyline, level: .aboveRoads)

        // Set the region
        mapView.setRegion(region, animated: true)

        return mapView
    }

    func updateUIView(_ mapView: MKMapView, context: Context) {
        // Update region if it changed
        mapView.setRegion(region, animated: true)
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, MKMapViewDelegate {
        var parent: TrackMapView

        init(_ parent: TrackMapView) {
            self.parent = parent
        }

        func mapView(_ mapView: MKMapView, rendererFor overlay: MKOverlay) -> MKOverlayRenderer {
            if let polyline = overlay as? MKPolyline {
                let renderer = MKPolylineRenderer(polyline: polyline)
                renderer.strokeColor = parent.track.color
                renderer.lineWidth = 4.0
                return renderer
            }
            return MKOverlayRenderer(overlay: overlay)
        }

        func mapView(_ mapView: MKMapView, regionDidChangeAnimated animated: Bool) {
            parent.region = mapView.region
        }
    }
}

struct TrimTrackView: View {
    let track: TrackSegment
    @Binding var startDate: Date
    @Binding var endDate: Date
    let onTrim: (Date, Date) -> Void

    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationStack {
            VStack {
                Text("Select the time range to keep")
                    .font(.headline)
                    .padding()

                VStack(alignment: .leading) {
                    Text("Start Time")
                        .font(.subheadline)
                    DatePicker("", selection: $startDate, in: track.startedAt...(track.endedAt ?? Date()))
                        .datePickerStyle(.compact)
                        .labelsHidden()
                }
                .padding()

                VStack(alignment: .leading) {
                    Text("End Time")
                        .font(.subheadline)
                    DatePicker("", selection: $endDate, in: startDate...(track.endedAt ?? Date()))
                        .datePickerStyle(.compact)
                        .labelsHidden()
                }
                .padding()

                Button("Trim Track") {
                    onTrim(startDate, endDate)
                    dismiss()
                }
                .buttonStyle(.borderedProminent)
                .padding()
            }
            .navigationTitle("Trim Track")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: items, applicationActivities: nil)
        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {
        // Nothing to update
    }
}

// MARK: - Formatters

private let itemFormatter: DateFormatter = {
    let formatter = DateFormatter()
    formatter.dateStyle = .short
    formatter.timeStyle = .medium
    return formatter
}()
