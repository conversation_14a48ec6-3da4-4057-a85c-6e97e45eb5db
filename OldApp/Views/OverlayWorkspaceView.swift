import SwiftUI
import CoreData

struct OverlayWorkspaceView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @EnvironmentObject private var overlayPublisher: OverlayPublisher

    @State private var editingOverlay: Overlay?
    @State private var isEditorMode = false

    var body: some View {
        NavigationStack {
            OverlayListView(editingOverlay: $editingOverlay, isEditorMode: $isEditorMode)
                .environmentObject(overlayPublisher)
                .navigationDestination(isPresented: $isEditorMode) {
                    if let overlay = editingOverlay {
                        OverlayEditorView(overlay: overlay)
                            .environmentObject(overlayPublisher)
                    }
                }
                .navigationTitle("Overlays")
        }
        .environmentObject(overlayPublisher)
    }
}
