import SwiftUI
import CoreData
import MapKit
import UIKit
import CoreLocation

// Class to manage the working state of an overlay during editing
class WorkingOverlayState: ObservableObject {
    @Published var workingMatrix: Matrix3x3
    @Published var opacity: Double
    @Published var rotation: Double
    @Published var scale: Double
    @Published var centerLat: Double
    @Published var centerLon: Double

    private let overlay: Overlay

    init(overlay: Overlay) {
        self.overlay = overlay

        // Initialize from overlay
        self.opacity = overlay.opacity
        self.rotation = overlay.rotation
        self.scale = overlay.relScale
        self.centerLat = overlay.centerLat
        self.centerLon = overlay.centerLon

        // Initialize matrix from overlay or create a new one
        if let transformData = overlay.transform,
           let matrix = try? JSONDecoder().decode(Matrix3x3.self, from: transformData) {
            self.workingMatrix = matrix
        } else {
            // Create a new matrix based on basic parameters
            var matrix = Matrix3x3()

            // Apply scale first
            matrix = Matrix3x3.scaling(sx: overlay.relScale, sy: overlay.relScale) * matrix

            // Apply rotation
            matrix = Matrix3x3.rotation(angle: overlay.rotation) * matrix

            // No translation needed - we use centerLat/centerLon for positioning

            self.workingMatrix = matrix
        }
    }

    // Update the matrix when rotation changes
    func updateRotation(_ newRotation: Double) {
        rotation = newRotation

        // Create a new transform matrix
        var matrix = Matrix3x3()

        // Apply scale first
        matrix = Matrix3x3.scaling(sx: scale, sy: scale) * matrix

        // Apply rotation
        matrix = Matrix3x3.rotation(angle: rotation) * matrix

        // No translation needed - we use centerLat/centerLon for positioning

        workingMatrix = matrix
    }

    // Update the matrix when scale changes
    func updateScale(_ newScale: Double) {
        scale = newScale

        // Create a new transform matrix
        var matrix = Matrix3x3()

        // Apply scale first
        matrix = Matrix3x3.scaling(sx: scale, sy: scale) * matrix

        // Apply rotation
        matrix = Matrix3x3.rotation(angle: rotation) * matrix

        // No translation needed - we use centerLat/centerLon for positioning

        workingMatrix = matrix
    }

    // Save changes back to the overlay
    func saveChanges() {
        // Normalize the matrix
        let normalizedMatrix = workingMatrix.normalized

        // Update the overlay
        overlay.opacity = opacity
        overlay.rotation = rotation
        overlay.relScale = scale

        // Save the matrix
        do {
            overlay.transform = try JSONEncoder().encode(normalizedMatrix)
        } catch {
            print("Error encoding matrix: \(error)")
        }

        // Update the timestamp
        overlay.updatedAt = Date()

        // Save the context
        do {
            try overlay.managedObjectContext?.save()
        } catch {
            print("Error saving context: \(error)")
        }
    }

    // Update the center coordinates
    func updateCenter(latitude: Double, longitude: Double) {
        centerLat = latitude
        centerLon = longitude
        overlay.centerLat = latitude
        overlay.centerLon = longitude
    }
}

struct OverlayEditorView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var overlayPublisher: OverlayPublisher

    let overlay: Overlay
    @StateObject private var workingState: WorkingOverlayState
    @State private var image: UIImage?
    @State private var showMap = true
    @State private var centerImageMode = true // Always center the image
    @State private var showingImagePicker = false
    @State private var region = MKCoordinateRegion(
        center: CLLocationCoordinate2D(latitude: 47.073286, longitude: -80.102979),
        span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
    )

    init(overlay: Overlay) {
        self.overlay = overlay
        _workingState = StateObject(wrappedValue: WorkingOverlayState(overlay: overlay))

        // Always initialize region with overlay center or default coordinates
        let centerLat = overlay.centerLat != 0 ? overlay.centerLat : 47.073286
        let centerLon = overlay.centerLon != 0 ? overlay.centerLon : -80.102979

        _region = State(initialValue: MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: centerLat, longitude: centerLon),
            span: MKCoordinateSpan(latitudeDelta: 0.05, longitudeDelta: 0.05)
        ))

        print("Initializing OverlayEditorView with center: \(centerLat), \(centerLon)")
    }

    var body: some View {
        VStack {
            // Toggle between map and image view
            VStack(spacing: 8) {
                Picker("View Mode", selection: $showMap) {
                    Text("Map View").tag(true)
                    Text("Image View").tag(false)
                }
                .pickerStyle(SegmentedPickerStyle())

                // No toolbar needed as we're simplifying the UI
            }
            .padding(.horizontal)

            Group {
                if showMap {
                    // Map view with overlay - simplified
                    ZStack {
                        MapViewWithOverlay(
                            region: $region,
                            overlay: overlay,
                            workingState: workingState,
                            centerImageMode: $centerImageMode
                        )
                        .frame(maxWidth: .infinity, maxHeight: .infinity)

                        // Map controls
                        VStack {
                            Spacer()

                            HStack {
                                // Center map on user location
                                Button(action: {
                                    if let location = LocationManager.shared.location {
                                        region.center = location.coordinate
                                    }
                                }) {
                                    Image(systemName: "location.circle")
                                        .padding()
                                        .background(Color.white.opacity(0.8))
                                        .clipShape(Circle())
                                }

                                // Zoom controls
                                Button(action: {
                                    // Zoom in - use a smaller factor for finer control
                                    region.span.latitudeDelta /= 1.5
                                    region.span.longitudeDelta /= 1.5

                                    // Ensure we don't zoom in too far
                                    let minSpan = 0.0001 // Minimum span to prevent excessive zoom
                                    region.span.latitudeDelta = max(region.span.latitudeDelta, minSpan)
                                    region.span.longitudeDelta = max(region.span.longitudeDelta, minSpan)

                                    // Trigger an update of the overlay
                                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                                       let keyWindow = windowScene.windows.first {
                                        let mapView = findMapView(in: keyWindow)
                                        NotificationCenter.default.post(name: NSNotification.Name("UpdateOverlay"), object: mapView)
                                    }
                                }) {
                                    Image(systemName: "plus.circle")
                                        .padding()
                                        .background(Color.white.opacity(0.8))
                                        .clipShape(Circle())
                                }

                                Button(action: {
                                    // Zoom out - use a smaller factor for finer control
                                    region.span.latitudeDelta *= 1.5
                                    region.span.longitudeDelta *= 1.5

                                    // Ensure we don't zoom out too far
                                    let maxSpan = 180.0 // Maximum span to prevent excessive zoom out
                                    region.span.latitudeDelta = min(region.span.latitudeDelta, maxSpan)
                                    region.span.longitudeDelta = min(region.span.longitudeDelta, maxSpan)

                                    // Trigger an update of the overlay
                                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                                       let keyWindow = windowScene.windows.first {
                                        let mapView = findMapView(in: keyWindow)
                                        NotificationCenter.default.post(name: NSNotification.Name("UpdateOverlay"), object: mapView)
                                    }
                                }) {
                                    Image(systemName: "minus.circle")
                                        .padding()
                                        .background(Color.white.opacity(0.8))
                                        .clipShape(Circle())
                                }
                            }
                            .padding()
                        }
                        .frame(maxWidth: .infinity, alignment: .trailing)
                    }
                } else {
                    // Image canvas
                    ZStack {
                        Color.gray.opacity(0.2)

                        if let image = image {
                            Image(uiImage: image)
                                .resizable()
                                .scaledToFit()
                                .opacity(workingState.opacity)
                                .rotationEffect(Angle(radians: workingState.rotation))
                                .scaleEffect(CGFloat(workingState.scale))
                                .overlay(
                                    // Show a warning if this is a placeholder image
                                    Group {
                                        if image == ImageManager.shared.getPlaceholderImage() {
                                            VStack {
                                                Text("Image Not Found")
                                                    .font(.headline)
                                                    .foregroundColor(.white)
                                                    .padding(8)
                                                    .background(Color.red.opacity(0.8))
                                                    .cornerRadius(8)

                                                Text("Tap to select a new image")
                                                    .font(.subheadline)
                                                    .foregroundColor(.white)
                                                    .padding(8)
                                                    .background(Color.blue.opacity(0.8))
                                                    .cornerRadius(8)
                                                    .padding(.top, 8)
                                            }
                                            .padding()
                                        }
                                    }
                                )
                                .onTapGesture {
                                    // If this is a placeholder, allow selecting a new image
                                    if image == ImageManager.shared.getPlaceholderImage() {
                                        showingImagePicker = true
                                    }
                                }
                        } else {
                            Text("Loading image...")
                        }
                    }
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .padding()

            // Control sliders
            VStack(spacing: 20) {
                // Opacity slider
                HStack {
                    Text("Opacity")
                        .frame(width: 80, alignment: .leading)

                    Slider(value: $workingState.opacity, in: 0.1...1.0)
                        .onChange(of: workingState.opacity) { oldValue, newValue in
                            // Trigger immediate update - find the map view first
                            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                               let keyWindow = windowScene.windows.first {
                                // Find the first MKMapView
                                let mapView = findMapView(in: keyWindow)
                                NotificationCenter.default.post(name: NSNotification.Name("UpdateOverlay"), object: mapView)
                            }
                        }

                    Text(String(format: "%.1f", workingState.opacity))
                        .frame(width: 40)
                }

                // Rotation slider
                HStack {
                    Text("Rotation")
                        .frame(width: 80, alignment: .leading)

                    Slider(value: $workingState.rotation, in: 0...Double.pi*2)
                        .onChange(of: workingState.rotation) { oldValue, newValue in
                            // Update the matrix
                            self.workingState.updateRotation(newValue)
                            // Trigger immediate update - find the map view first
                            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                               let keyWindow = windowScene.windows.first {
                                // Find the first MKMapView
                                let mapView = findMapView(in: keyWindow)
                                NotificationCenter.default.post(name: NSNotification.Name("UpdateOverlay"), object: mapView)
                            }
                        }

                    Text(String(format: "%.0f°", workingState.rotation * 180 / .pi))
                        .frame(width: 40)
                }

                // Scale slider
                HStack {
                    Text("Scale")
                        .frame(width: 80, alignment: .leading)

                    Slider(value: $workingState.scale, in: 0.5...5.0) // Increased max scale to 5.0
                        .onChange(of: workingState.scale) { oldValue, newValue in
                            // Update the matrix
                            self.workingState.updateScale(newValue)
                            // Trigger immediate update - find the map view first
                            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                               let keyWindow = windowScene.windows.first {
                                // Find the first MKMapView
                                let mapView = findMapView(in: keyWindow)
                                NotificationCenter.default.post(name: NSNotification.Name("UpdateOverlay"), object: mapView)
                            }
                        }

                    Text(String(format: "%.1f", workingState.scale))
                        .frame(width: 40)
                }
            }
            .padding()
        }
        .navigationTitle("Edit Overlay")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Save") {
                    saveOverlay()
                }
            }
        }
        .onAppear {
            loadImage()
        }
        .onDisappear {
            // Clean up notification observers
            NotificationCenter.default.removeObserver(self, name: NSNotification.Name("UpdateOverlay"), object: nil)
        }
        .sheet(isPresented: $showingImagePicker) {
            ImagePicker(onImageSelected: { newImage, url in
                // Update the overlay with the new image
                if let url = url {
                    overlay.imageURL = url
                    try? overlay.managedObjectContext?.save()

                    // Update the displayed image
                    self.image = newImage

                    // Notify that the overlay has changed
                    overlayPublisher.overlayDidChange()
                }
            })
        }
    }

    private func loadImage() {
        // Use ImageManager to load the image
        DispatchQueue.global().async {
            if let loadedImage = ImageManager.shared.loadImage(from: self.overlay.imageURL) {
                // Make sure the overlay has the correct URL
                ImageManager.shared.updateOverlayImageURL(self.overlay)

                DispatchQueue.main.async {
                    self.image = loadedImage
                }
            } else {
                print("OverlayEditorView: Failed to load image for overlay")
            }
        }
    }

    private func saveOverlay() {
        // 1. Capture current MKMapView camera (center and zoom)
        // This is already done via the region binding

        // 2. Update center coordinates from the map region
        workingState.updateCenter(latitude: region.center.latitude, longitude: region.center.longitude)

        // 3. Normalize workingMatrix and save to overlay.transform
        // 4. Persist other derived fields (centerLat, centerLon, relScale, rotation)
        workingState.saveChanges()

        // Notify that the overlay has changed so Map Screen reloads
        overlayPublisher.overlayDidChange()

        // Dismiss the editor
        // This will return to the Map Screen which will display the overlay
        // at the exact same on-screen location and scale that the user saw at save time
        dismiss()
    }

    // Helper to find MKMapView in view hierarchy
    private func findMapView(in view: UIView) -> MKMapView? {
        // Check if this view is an MKMapView
        if let mapView = view as? MKMapView {
            return mapView
        }

        // Check all subviews
        for subview in view.subviews {
            if let mapView = findMapView(in: subview) {
                return mapView
            }
        }

        return nil
    }
}
