import SwiftUI
import Combine

struct AppView: View {
    @Environment(\.horizontalSizeClass) private var horizontalSizeClass
    @EnvironmentObject private var overlayPublisher: OverlayPublisher

    var body: some View {
        if horizontalSizeClass == .compact {
            // iPhone layout - NavigationStack
            NavigationStack {
                MapScreenView()
                    .environmentObject(overlayPublisher)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarTrailing) {
                            NavigationLink(destination: OverlayWorkspaceView().environmentObject(overlayPublisher)) {
                                Image(systemName: "photo.stack")
                                    .foregroundColor(.blue)
                            }
                        }
                    }
            }
        } else {
            // iPad layout - NavigationSplitView
            NavigationSplitView {
                List {
                    NavigationLink(destination: MapScreenView().environmentObject(overlayPublisher)) {
                        Label("Map", systemImage: "map")
                    }

                    NavigationLink(destination: OverlayWorkspaceView().environmentObject(overlayPublisher)) {
                        Label("Overlays", systemImage: "photo.stack")
                    }

                    NavigationLink(destination: NavigationStack { TrackListView() }) {
                        Label("GPS Tracks", systemImage: "map.fill")
                    }
                }
                .navigationTitle("FogOfWar")
            } detail: {
                MapScreenView()
                    .environmentObject(overlayPublisher)
            }
        }
    }
}

// Publisher for overlay changes
class OverlayPublisher: ObservableObject {
    @Published var overlayUpdated = UUID()

    func overlayDidChange() {
        DispatchQueue.main.async {
            self.overlayUpdated = UUID()
        }
    }
}
