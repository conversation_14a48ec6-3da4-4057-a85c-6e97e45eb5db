import CoreLocation
import Combine
import UIKit
import SwiftUI

class LocationManager: NSObject, ObservableObject {
    // Shared instance
    static let shared = LocationManager()

    private let locationManager = CLLocationManager()

    // Published properties
    @Published var location: CLLocation?
    @Published var authorizationStatus: CLAuthorizationStatus = .notDetermined

    // Track segment being recorded
    var currentTrackSegment: TrackSegment?

    // Configuration
    private let desiredAccuracy: CLLocationAccuracy = kCLLocationAccuracyBestForNavigation
    private let distanceFilter: CLLocationDistance = 5.0  // meters
    private let accuracyThreshold: CLLocationAccuracy = 10.0  // meters
    private let minimumBatteryLevel: Float = 0.2  // 20%
    private let minimumTimeInterval: TimeInterval = 1.0  // 1 second

    // Last recorded location and timestamp
    private var lastRecordedLocation: CLLocation?
    private var lastRecordedTime: Date?

    // Battery monitoring
    private var batteryMonitor: BatteryMonitor

    // Background task identifier
    private var backgroundTaskID: UIBackgroundTaskIdentifier = .invalid

    override init() {
        // Initialize battery monitoring
        batteryMonitor = BatteryMonitor()

        super.init()

        // Configure location manager
        locationManager.delegate = self
        locationManager.desiredAccuracy = desiredAccuracy
        locationManager.distanceFilter = distanceFilter
        locationManager.pausesLocationUpdatesAutomatically = true

        // Don't set allowsBackgroundLocationUpdates here
        // We'll set it only when we have the proper authorization

        // Check current authorization status
        authorizationStatus = locationManager.authorizationStatus
    }

    // MARK: - Public Methods

    /// Request location permissions
    func requestPermissions() {
        locationManager.requestAlwaysAuthorization()
    }

    /// Request a single location update
    func requestLocation() {
        locationManager.requestLocation()
    }

    /// Start continuous location tracking
    func startTracking(with trackSegment: TrackSegment) {
        // Store the track segment
        currentTrackSegment = trackSegment

        // Request permissions if needed
        if authorizationStatus != .authorizedAlways && authorizationStatus != .authorizedWhenInUse {
            requestPermissions()
            return
        }

        // Configure for background updates if we have always authorization
        if authorizationStatus == .authorizedAlways {
            locationManager.allowsBackgroundLocationUpdates = true
            locationManager.showsBackgroundLocationIndicator = true
        } else {
            // When in use only - don't allow background updates
            locationManager.allowsBackgroundLocationUpdates = false
        }

        // Start location updates
        locationManager.startUpdatingLocation()

        // Begin background task if we're allowed to run in background
        if locationManager.allowsBackgroundLocationUpdates {
            // End any existing background task
            endBackgroundTaskIfNeeded()

            // Start a new background task
            backgroundTaskID = UIApplication.shared.beginBackgroundTask(withName: "TrackRecording") { [weak self] in
                // This is the expiration handler - clean up if the task expires
                self?.endBackgroundTaskIfNeeded()
            }
        }
    }

    /// Stop location tracking
    func stopTracking() {
        locationManager.stopUpdatingLocation()
        currentTrackSegment = nil
        lastRecordedLocation = nil
        lastRecordedTime = nil
        endBackgroundTaskIfNeeded()

        // Reset battery monitor consecutive readings
        batteryMonitor.consecutiveLowBatteryReadings = 0
    }

    /// End the background task if one is running
    private func endBackgroundTaskIfNeeded() {
        if backgroundTaskID != .invalid {
            UIApplication.shared.endBackgroundTask(backgroundTaskID)
            backgroundTaskID = .invalid
        }
    }
}

// MARK: - CLLocationManagerDelegate

extension LocationManager: CLLocationManagerDelegate {
    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        let newStatus = manager.authorizationStatus
        let previousStatus = authorizationStatus
        authorizationStatus = newStatus

        // If authorization status changed and we have a pending track segment
        if previousStatus != newStatus, let segment = currentTrackSegment {
            if newStatus == .authorizedAlways || newStatus == .authorizedWhenInUse {
                // We got authorization, start tracking
                startTracking(with: segment)
            } else if newStatus == .denied || newStatus == .restricted {
                // We lost authorization, stop tracking
                stopTracking()
            }
        }

        // If we're currently tracking and authorization changed, update background settings
        if currentTrackSegment != nil && previousStatus != newStatus {
            // If we now have always authorization, enable background updates
            if newStatus == .authorizedAlways {
                locationManager.allowsBackgroundLocationUpdates = true
                locationManager.showsBackgroundLocationIndicator = true
            } else {
                // Otherwise disable background updates
                locationManager.allowsBackgroundLocationUpdates = false
            }
        }
    }

    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        // Update the current location with the most recent one
        if let location = locations.last {
            self.location = location

            // If we're tracking, process the location for the current segment
            if let segment = currentTrackSegment {
                processLocationForTracking(location, segment: segment)
            }
        }
    }

    /// Process a location update for tracking
    private func processLocationForTracking(_ location: CLLocation, segment: TrackSegment) {
        // Step 1: Check accuracy - discard fixes with horizontalAccuracy > 10m
        guard location.horizontalAccuracy <= accuracyThreshold else {
            print("LocationManager: Discarding location with insufficient accuracy: \(location.horizontalAccuracy)m")
            return
        }

        // Step 2: Check battery level - pause tracking if battery < 20%
        guard batteryMonitor.batteryLevel > minimumBatteryLevel else {
            print("LocationManager: Battery level too low, pausing tracking")
            // Don't stop tracking immediately, just don't record this point
            // If battery continues to be low, we'll eventually stop

            // If we've received 5 consecutive low battery readings, stop tracking
            batteryMonitor.consecutiveLowBatteryReadings += 1
            if batteryMonitor.consecutiveLowBatteryReadings >= 5 {
                print("LocationManager: Battery consistently low, stopping tracking")
                stopTracking()
            }
            return
        }

        // Reset consecutive low battery readings
        batteryMonitor.consecutiveLowBatteryReadings = 0

        // Step 3: Apply sampling rules - 1s or 5m intervals (whichever is larger)
        let now = Date()

        // Check if we have a previous location to compare with
        if let lastLocation = lastRecordedLocation, let lastTime = lastRecordedTime {
            // Calculate time since last recorded location
            let timeSinceLast = now.timeIntervalSince(lastTime)

            // Calculate distance from last recorded location
            let distanceFromLast = location.distance(from: lastLocation)

            // Only record if either time or distance threshold is met
            if timeSinceLast < minimumTimeInterval && distanceFromLast < distanceFilter {
                // Skip this location - too soon or too close
                return
            }
        }

        // If we get here, the location passed all filters and should be recorded

        // Update last recorded location and time
        lastRecordedLocation = location
        lastRecordedTime = now

        // Add the location to the segment
        segment.addLocation(location)

        // If we're in background mode, extend the background task
        if UIApplication.shared.applicationState == .background && backgroundTaskID != .invalid {
            // End the current task and start a new one to extend the time
            endBackgroundTaskIfNeeded()

            backgroundTaskID = UIApplication.shared.beginBackgroundTask(withName: "TrackRecording") { [weak self] in
                self?.endBackgroundTaskIfNeeded()
            }
        }
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("Location manager failed with error: \(error.localizedDescription)")
    }
}

// MARK: - Battery Monitoring

class BatteryMonitor: ObservableObject {
    // Current battery level
    var batteryLevel: Float {
        UIDevice.current.batteryLevel
    }

    // Battery state (charging, full, unplugged)
    var batteryState: UIDevice.BatteryState {
        UIDevice.current.batteryState
    }

    // Track consecutive low battery readings
    var consecutiveLowBatteryReadings: Int = 0

    // Minimum acceptable battery level
    private let minimumBatteryLevel: Float = 0.2 // 20%

    // Published property for low battery warning
    @Published var isLowBattery: Bool = false

    // Published property for battery status
    @Published var batteryStatus: String = ""

    init() {
        UIDevice.current.isBatteryMonitoringEnabled = true

        // Register for battery level notifications
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(batteryLevelDidChange),
            name: UIDevice.batteryLevelDidChangeNotification,
            object: nil
        )

        // Register for battery state notifications
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(batteryStateDidChange),
            name: UIDevice.batteryStateDidChangeNotification,
            object: nil
        )

        // Initial update
        updateBatteryStatus()
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    @objc private func batteryLevelDidChange(_ notification: Notification) {
        updateBatteryStatus()
    }

    @objc private func batteryStateDidChange(_ notification: Notification) {
        updateBatteryStatus()
    }

    private func updateBatteryStatus() {
        // Update low battery status
        isLowBattery = batteryLevel < minimumBatteryLevel

        // Update consecutive low battery readings
        if isLowBattery {
            consecutiveLowBatteryReadings += 1
        } else {
            consecutiveLowBatteryReadings = 0
        }

        // Update battery status string
        let percentage = Int(batteryLevel * 100)
        var stateString = ""

        switch batteryState {
        case .charging:
            stateString = "Charging"
        case .full:
            stateString = "Full"
        case .unplugged:
            stateString = "Unplugged"
        case .unknown:
            stateString = "Unknown"
        @unknown default:
            stateString = "Unknown"
        }

        batteryStatus = "\(percentage)% (\(stateString))"

        // Print debug info
        print("BatteryMonitor: Level: \(percentage)%, State: \(stateString), Low: \(isLowBattery), Consecutive Low: \(consecutiveLowBatteryReadings)")
    }
}
