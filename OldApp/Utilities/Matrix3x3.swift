import Foundation
import simd

/// A wrapper around simd_double3x3 that provides Codable support and utility methods
struct Matrix3x3: Codable, Equatable {
    // The underlying simd matrix
    private var matrix: simd_double3x3
    
    // MARK: - Initialization
    
    /// Initialize with a simd_double3x3
    init(_ matrix: simd_double3x3) {
        self.matrix = matrix
    }
    
    /// Initialize with 9 values in row-major order
    init(values: [Double]) {
        precondition(values.count == 9, "Matrix3x3 requires exactly 9 values")
        
        // Create rows from the values (row-major order)
        let row0 = SIMD3<Double>(values[0], values[1], values[2])
        let row1 = SIMD3<Double>(values[3], values[4], values[5])
        let row2 = SIMD3<Double>(values[6], values[7], values[8])
        
        self.matrix = simd_double3x3(rows: [row0, row1, row2])
    }
    
    /// Initialize with identity matrix
    init() {
        self.matrix = simd_double3x3(1.0)
    }
    
    // MARK: - Properties
    
    /// Get the inverse of this matrix
    var inverse: Matrix3x3 {
        Matrix3x3(simd_inverse(matrix))
    }
    
    /// Get the determinant of this matrix
    var determinant: Double {
        simd_determinant(matrix)
    }
    
    /// Get the values as a flattened array in row-major order
    var values: [Double] {
        [
            matrix[0, 0], matrix[0, 1], matrix[0, 2],
            matrix[1, 0], matrix[1, 1], matrix[1, 2],
            matrix[2, 0], matrix[2, 1], matrix[2, 2]
        ]
    }
    
    /// Get the normalized matrix (bottom-right element set to 1.0)
    var normalized: Matrix3x3 {
        guard matrix[2, 2] != 0 else { return self }
        let scale = 1.0 / matrix[2, 2]
        var normalizedMatrix = matrix
        normalizedMatrix.columns.0 *= scale
        normalizedMatrix.columns.1 *= scale
        normalizedMatrix.columns.2 *= scale
        return Matrix3x3(normalizedMatrix)
    }
    
    // MARK: - Subscript access
    
    subscript(row: Int, column: Int) -> Double {
        get {
            precondition(row >= 0 && row < 3 && column >= 0 && column < 3, "Index out of bounds")
            return matrix[row, column]
        }
        set {
            precondition(row >= 0 && row < 3 && column >= 0 && column < 3, "Index out of bounds")
            matrix[row, column] = newValue
        }
    }
    
    // MARK: - Transformations
    
    /// Apply this transformation to a point
    func transform(point: CGPoint) -> CGPoint {
        let homogeneous = simd_double3(Double(point.x), Double(point.y), 1.0)
        let transformed = matrix * homogeneous
        
        // Convert back from homogeneous coordinates
        if transformed.z != 0 {
            return CGPoint(x: transformed.x / transformed.z, y: transformed.y / transformed.z)
        } else {
            return CGPoint(x: transformed.x, y: transformed.y)
        }
    }
    
    /// Apply this transformation to a rectangle
    func transform(rect: CGRect) -> CGRect {
        let topLeft = transform(point: CGPoint(x: rect.minX, y: rect.minY))
        let topRight = transform(point: CGPoint(x: rect.maxX, y: rect.minY))
        let bottomLeft = transform(point: CGPoint(x: rect.minX, y: rect.maxY))
        let bottomRight = transform(point: CGPoint(x: rect.maxX, y: rect.maxY))
        
        let minX = min(topLeft.x, topRight.x, bottomLeft.x, bottomRight.x)
        let maxX = max(topLeft.x, topRight.x, bottomLeft.x, bottomRight.x)
        let minY = min(topLeft.y, topRight.y, bottomLeft.y, bottomRight.y)
        let maxY = max(topLeft.y, topRight.y, bottomLeft.y, bottomRight.y)
        
        return CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
    }
    
    // MARK: - Codable
    
    enum CodingKeys: String, CodingKey {
        case values
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let values = try container.decode([Double].self, forKey: .values)
        self.init(values: values)
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(values, forKey: .values)
    }
    
    // MARK: - Static factory methods
    
    /// Create a translation matrix
    static func translation(tx: Double, ty: Double) -> Matrix3x3 {
        Matrix3x3(simd_double3x3(
            SIMD3<Double>(1, 0, tx),
            SIMD3<Double>(0, 1, ty),
            SIMD3<Double>(0, 0, 1)
        ))
    }
    
    /// Create a scaling matrix
    static func scaling(sx: Double, sy: Double) -> Matrix3x3 {
        Matrix3x3(simd_double3x3(
            SIMD3<Double>(sx, 0, 0),
            SIMD3<Double>(0, sy, 0),
            SIMD3<Double>(0, 0, 1)
        ))
    }
    
    /// Create a rotation matrix (angle in radians)
    static func rotation(angle: Double) -> Matrix3x3 {
        let cosAngle = cos(angle)
        let sinAngle = sin(angle)
        return Matrix3x3(simd_double3x3(
            SIMD3<Double>(cosAngle, -sinAngle, 0),
            SIMD3<Double>(sinAngle, cosAngle, 0),
            SIMD3<Double>(0, 0, 1)
        ))
    }
}

// MARK: - Matrix operations

extension Matrix3x3 {
    static func * (lhs: Matrix3x3, rhs: Matrix3x3) -> Matrix3x3 {
        Matrix3x3(lhs.matrix * rhs.matrix)
    }
}
