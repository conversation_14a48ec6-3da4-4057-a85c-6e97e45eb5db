import Foundation
import CoreData
import CoreLocation
import UIKit
import SwiftUI

/// Manages track-related operations
class TrackManager {
    static let shared = TrackManager()
    
    private init() {}
    
    // MARK: - Track Editing
    
    /// Trim a track segment to the specified time range
    func trimTrack(_ segment: TrackSegment, from startTime: Date, to endTime: Date, context: NSManagedObjectContext) -> TrackSegment? {
        guard startTime < endTime else {
            print("TrackManager: Invalid time range for trimming")
            return nil
        }
        
        // Get all points in the segment
        let allPoints = segment.sortedPoints
        
        // Filter points within the time range
        let pointsInRange = allPoints.filter { point in
            return point.timestamp >= startTime && point.timestamp <= endTime
        }
        
        guard !pointsInRange.isEmpty else {
            print("TrackManager: No points in the specified time range")
            return nil
        }
        
        // Create a new segment with the filtered points
        let newSegment = TrackSegment(context: context)
        newSegment.id = UUID()
        newSegment.startedAt = startTime
        newSegment.endedAt = endTime
        newSegment.colorHex = segment.colorHex
        
        // Add the filtered points to the new segment
        for point in pointsInRange {
            let newPoint = TrackPoint(context: context)
            newPoint.lat = point.lat
            newPoint.lon = point.lon
            newPoint.timestamp = point.timestamp
            newPoint.horizontalAccuracy = point.horizontalAccuracy
            newPoint.altitude = point.altitude
            newPoint.segment = newSegment
        }
        
        // Save the context
        do {
            try context.save()
            return newSegment
        } catch {
            print("TrackManager: Error saving trimmed track: \(error)")
            return nil
        }
    }
    
    /// Split a track segment at the specified time
    func splitTrack(_ segment: TrackSegment, at splitTime: Date, context: NSManagedObjectContext) -> (TrackSegment, TrackSegment)? {
        // Get all points in the segment
        let allPoints = segment.sortedPoints
        
        // Filter points before and after the split time
        let pointsBefore = allPoints.filter { $0.timestamp <= splitTime }
        let pointsAfter = allPoints.filter { $0.timestamp > splitTime }
        
        guard !pointsBefore.isEmpty && !pointsAfter.isEmpty else {
            print("TrackManager: Cannot split track - not enough points on both sides")
            return nil
        }
        
        // Create two new segments
        let segment1 = TrackSegment(context: context)
        segment1.id = UUID()
        segment1.startedAt = segment.startedAt
        segment1.endedAt = splitTime
        segment1.colorHex = segment.colorHex
        
        let segment2 = TrackSegment(context: context)
        segment2.id = UUID()
        segment2.startedAt = splitTime
        segment2.endedAt = segment.endedAt
        segment2.colorHex = segment.colorHex
        
        // Add points to the first segment
        for point in pointsBefore {
            let newPoint = TrackPoint(context: context)
            newPoint.lat = point.lat
            newPoint.lon = point.lon
            newPoint.timestamp = point.timestamp
            newPoint.horizontalAccuracy = point.horizontalAccuracy
            newPoint.altitude = point.altitude
            newPoint.segment = segment1
        }
        
        // Add points to the second segment
        for point in pointsAfter {
            let newPoint = TrackPoint(context: context)
            newPoint.lat = point.lat
            newPoint.lon = point.lon
            newPoint.timestamp = point.timestamp
            newPoint.horizontalAccuracy = point.horizontalAccuracy
            newPoint.altitude = point.altitude
            newPoint.segment = segment2
        }
        
        // Save the context
        do {
            try context.save()
            return (segment1, segment2)
        } catch {
            print("TrackManager: Error saving split tracks: \(error)")
            return nil
        }
    }
    
    // MARK: - GPX Export
    
    /// Export a track segment as GPX
    func exportAsGPX(_ segment: TrackSegment) -> String {
        return segment.exportAsGPX()
    }
    
    /// Export multiple track segments as a single GPX file
    func exportMultipleTracksAsGPX(_ segments: [TrackSegment]) -> String {
        let dateFormatter = ISO8601DateFormatter()
        
        var gpx = """
        <?xml version="1.0" encoding="UTF-8"?>
        <gpx version="1.1" creator="FogOfWarV2" xmlns="http://www.topografix.com/GPX/1/1">
        """
        
        for segment in segments {
            gpx += """
              <trk>
                <name>Track \(dateFormatter.string(from: segment.startedAt))</name>
                <trkseg>
            """
            
            for point in segment.sortedPoints {
                gpx += """
                    <trkpt lat="\(point.lat)" lon="\(point.lon)">
                      <ele>\(point.altitude ?? 0)</ele>
                      <time>\(dateFormatter.string(from: point.timestamp))</time>
                    </trkpt>
                """
            }
            
            gpx += """
                </trkseg>
              </trk>
            """
        }
        
        gpx += """
        </gpx>
        """
        
        return gpx
    }
    
    /// Save GPX data to a file and return the URL
    func saveGPXToFile(_ gpxData: String, fileName: String = "track.gpx") -> URL? {
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        let fileURL = documentsDirectory.appendingPathComponent(fileName)
        
        do {
            try gpxData.write(to: fileURL, atomically: true, encoding: .utf8)
            return fileURL
        } catch {
            print("TrackManager: Error saving GPX file: \(error)")
            return nil
        }
    }
}
