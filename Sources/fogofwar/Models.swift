import Foundation
#if canImport(CloudKit)
import CloudKit
#endif

public final class Overlay: Codable, Equatable, Identifiable {
    public var id: UUID = .init()
    public var name: String = ""
    public var overlayDescription: String?
    public var imageLocalIdentifier: String?
    public var imageURL: URL?
    public var centerLatitude: Double = 0
    public var centerLongitude: Double = 0
    public var baselineFit: Double = 0
    public var relativeScale: Double = 1
    public var rotation: Double = 0
    public var opacity: Double = 1
    public var zOrder: Int32 = 0
    public var isVisible: Bool = true
    public var forwardMatrix: Data?
    public var createdAt: Date = .now
    public var updatedAt: Date = .now
    public var controlPoints: [OverlayControlPoint] = []

    public init() {}

#if canImport(CloudKit)
    public static let recordType = "Overlay"
    public var recordID: CKRecord.ID = CKRecord.ID(recordName: UUID().uuidString)

    public init(record: CKRecord) {
        self.recordID = record.recordID
        self.id = record["id"] as? UUID ?? UUID()
        self.name = record["name"] as? String ?? ""
        self.overlayDescription = record["overlayDescription"] as? String
        self.imageLocalIdentifier = record["imageLocalIdentifier"] as? String
        if let urlString = record["imageURL"] as? String { self.imageURL = URL(string: urlString) }
        self.centerLatitude = record["centerLatitude"] as? Double ?? 0
        self.centerLongitude = record["centerLongitude"] as? Double ?? 0
        self.baselineFit = record["baselineFit"] as? Double ?? 0
        self.relativeScale = record["relativeScale"] as? Double ?? 1
        self.rotation = record["rotation"] as? Double ?? 0
        self.opacity = record["opacity"] as? Double ?? 1
        self.zOrder = record["zOrder"] as? Int32 ?? 0
        self.isVisible = record["isVisible"] as? Bool ?? true
        self.forwardMatrix = record["forwardMatrix"] as? Data
        self.createdAt = record["createdAt"] as? Date ?? Date()
        self.updatedAt = record["updatedAt"] as? Date ?? Date()
    }

    public func toRecord() -> CKRecord {
        let rec = CKRecord(recordType: Self.recordType, recordID: recordID)
        rec["id"] = id as CKRecordValue
        rec["name"] = name as CKRecordValue
        rec["overlayDescription"] = overlayDescription as CKRecordValue?
        rec["imageLocalIdentifier"] = imageLocalIdentifier as CKRecordValue?
        rec["imageURL"] = imageURL?.absoluteString as CKRecordValue?
        rec["centerLatitude"] = centerLatitude as CKRecordValue
        rec["centerLongitude"] = centerLongitude as CKRecordValue
        rec["baselineFit"] = baselineFit as CKRecordValue
        rec["relativeScale"] = relativeScale as CKRecordValue
        rec["rotation"] = rotation as CKRecordValue
        rec["opacity"] = opacity as CKRecordValue
        rec["zOrder"] = zOrder as CKRecordValue
        rec["isVisible"] = isVisible as CKRecordValue
        rec["forwardMatrix"] = forwardMatrix as CKRecordValue?
        rec["createdAt"] = createdAt as CKRecordValue
        rec["updatedAt"] = updatedAt as CKRecordValue
        return rec
    }
#endif

    public static func == (lhs: Overlay, rhs: Overlay) -> Bool {
        lhs.id == rhs.id
    }
}

public final class OverlayControlPoint: Codable, Equatable, Identifiable {
    public var id: UUID = .init()
    public weak var overlay: Overlay?
    public var pixelX: Double = 0
    public var pixelY: Double = 0
    public var latitude: Double = 0
    public var longitude: Double = 0
    public var index: Int16 = 0

    public init() {}

#if canImport(CloudKit)
    public static let recordType = "OverlayControlPoint"
    public var recordID: CKRecord.ID = CKRecord.ID(recordName: UUID().uuidString)

    public init(record: CKRecord) {
        self.recordID = record.recordID
        self.id = record["id"] as? UUID ?? UUID()
        self.pixelX = record["pixelX"] as? Double ?? 0
        self.pixelY = record["pixelY"] as? Double ?? 0
        self.latitude = record["latitude"] as? Double ?? 0
        self.longitude = record["longitude"] as? Double ?? 0
        self.index = record["index"] as? Int16 ?? 0
    }

    public func toRecord() -> CKRecord {
        let rec = CKRecord(recordType: Self.recordType, recordID: recordID)
        rec["id"] = id as CKRecordValue
        rec["pixelX"] = pixelX as CKRecordValue
        rec["pixelY"] = pixelY as CKRecordValue
        rec["latitude"] = latitude as CKRecordValue
        rec["longitude"] = longitude as CKRecordValue
        rec["index"] = index as CKRecordValue
        return rec
    }
#endif

    public static func == (lhs: OverlayControlPoint, rhs: OverlayControlPoint) -> Bool {
        lhs.id == rhs.id
    }
}

public final class Track: Codable, Equatable, Identifiable {
    public var id: UUID = .init()
    public var name: String = ""
    public var trackDescription: String?
    public var colourHex: String = "#FF0000"
    public var startedAt: Date = .now
    public var endedAt: Date?
    public var lengthMetres: Double = 0
    public var durationSeconds: Double = 0
    public var isVisible: Bool = true
    public var points: [TrackPoint] = []

    public init() {}

#if canImport(CloudKit)
    public static let recordType = "Track"
    public var recordID: CKRecord.ID = CKRecord.ID(recordName: UUID().uuidString)

    public init(record: CKRecord) {
        self.recordID = record.recordID
        self.id = record["id"] as? UUID ?? UUID()
        self.name = record["name"] as? String ?? ""
        self.trackDescription = record["trackDescription"] as? String
        self.colourHex = record["colourHex"] as? String ?? "#FF0000"
        self.startedAt = record["startedAt"] as? Date ?? Date()
        self.endedAt = record["endedAt"] as? Date
        self.lengthMetres = record["lengthMetres"] as? Double ?? 0
        self.durationSeconds = record["durationSeconds"] as? Double ?? 0
        self.isVisible = record["isVisible"] as? Bool ?? true
    }

    public func toRecord() -> CKRecord {
        let rec = CKRecord(recordType: Self.recordType, recordID: recordID)
        rec["id"] = id as CKRecordValue
        rec["name"] = name as CKRecordValue
        rec["trackDescription"] = trackDescription as CKRecordValue?
        rec["colourHex"] = colourHex as CKRecordValue
        rec["startedAt"] = startedAt as CKRecordValue
        rec["endedAt"] = endedAt as CKRecordValue?
        rec["lengthMetres"] = lengthMetres as CKRecordValue
        rec["durationSeconds"] = durationSeconds as CKRecordValue
        rec["isVisible"] = isVisible as CKRecordValue
        return rec
    }
#endif

    public static func == (lhs: Track, rhs: Track) -> Bool {
        lhs.id == rhs.id
    }
}

public final class TrackPoint: Codable, Equatable, Identifiable {
    public var id: UUID = .init()
    public weak var track: Track?
    public var latitude: Double = 0
    public var longitude: Double = 0
    public var altitude: Double?
    public var horizontalAccuracy: Double = 0
    public var timestamp: Date = .now

    public init() {}

#if canImport(CloudKit)
    public static let recordType = "TrackPoint"
    public var recordID: CKRecord.ID = CKRecord.ID(recordName: UUID().uuidString)

    public init(record: CKRecord) {
        self.recordID = record.recordID
        self.id = record["id"] as? UUID ?? UUID()
        self.latitude = record["latitude"] as? Double ?? 0
        self.longitude = record["longitude"] as? Double ?? 0
        self.altitude = record["altitude"] as? Double
        self.horizontalAccuracy = record["horizontalAccuracy"] as? Double ?? 0
        self.timestamp = record["timestamp"] as? Date ?? Date()
    }

    public func toRecord() -> CKRecord {
        let rec = CKRecord(recordType: Self.recordType, recordID: recordID)
        rec["id"] = id as CKRecordValue
        rec["latitude"] = latitude as CKRecordValue
        rec["longitude"] = longitude as CKRecordValue
        rec["altitude"] = altitude as CKRecordValue?
        rec["horizontalAccuracy"] = horizontalAccuracy as CKRecordValue
        rec["timestamp"] = timestamp as CKRecordValue
        return rec
    }
#endif

    public static func == (lhs: TrackPoint, rhs: TrackPoint) -> Bool {
        lhs.id == rhs.id
    }
}

public final class MarkerCategory: Codable, Equatable, Identifiable {
    public var id: UUID = .init()
    public var name: String = ""
    public var iconName: String = "mappin"
    public var createdAt: Date = .now
    public var markers: [Marker] = []

    public init() {}

#if canImport(CloudKit)
    public static let recordType = "MarkerCategory"
    public var recordID: CKRecord.ID = CKRecord.ID(recordName: UUID().uuidString)

    public init(record: CKRecord) {
        self.recordID = record.recordID
        self.id = record["id"] as? UUID ?? UUID()
        self.name = record["name"] as? String ?? ""
        self.iconName = record["iconName"] as? String ?? "mappin"
        self.createdAt = record["createdAt"] as? Date ?? Date()
    }

    public func toRecord() -> CKRecord {
        let rec = CKRecord(recordType: Self.recordType, recordID: recordID)
        rec["id"] = id as CKRecordValue
        rec["name"] = name as CKRecordValue
        rec["iconName"] = iconName as CKRecordValue
        rec["createdAt"] = createdAt as CKRecordValue
        return rec
    }
#endif

    public static func == (lhs: MarkerCategory, rhs: MarkerCategory) -> Bool {
        lhs.id == rhs.id
    }
}

public final class Marker: Codable, Equatable, Identifiable {
    public var id: UUID = .init()
    public var name: String = ""
    public var markerDescription: String?
    public weak var category: MarkerCategory?
    public var iconNameOverride: String?
    public var photoAssetID: String = ""
    public var latitude: Double = 0
    public var longitude: Double = 0
    public var isVisible: Bool = true
    public var createdAt: Date = .now

    public init() {}

#if canImport(CloudKit)
    public static let recordType = "Marker"
    public var recordID: CKRecord.ID = CKRecord.ID(recordName: UUID().uuidString)

    public init(record: CKRecord) {
        self.recordID = record.recordID
        self.id = record["id"] as? UUID ?? UUID()
        self.name = record["name"] as? String ?? ""
        self.markerDescription = record["markerDescription"] as? String
        self.iconNameOverride = record["iconNameOverride"] as? String
        self.photoAssetID = record["photoAssetID"] as? String ?? ""
        self.latitude = record["latitude"] as? Double ?? 0
        self.longitude = record["longitude"] as? Double ?? 0
        self.isVisible = record["isVisible"] as? Bool ?? true
        self.createdAt = record["createdAt"] as? Date ?? Date()
    }

    public func toRecord() -> CKRecord {
        let rec = CKRecord(recordType: Self.recordType, recordID: recordID)
        rec["id"] = id as CKRecordValue
        rec["name"] = name as CKRecordValue
        rec["markerDescription"] = markerDescription as CKRecordValue?
        rec["iconNameOverride"] = iconNameOverride as CKRecordValue?
        rec["photoAssetID"] = photoAssetID as CKRecordValue
        rec["latitude"] = latitude as CKRecordValue
        rec["longitude"] = longitude as CKRecordValue
        rec["isVisible"] = isVisible as CKRecordValue
        rec["createdAt"] = createdAt as CKRecordValue
        return rec
    }
#endif

    public static func == (lhs: Marker, rhs: Marker) -> Bool {
        lhs.id == rhs.id
    }
}

public final class Area: Codable, Equatable, Identifiable {
    public var id: UUID = .init()
    public var name: String = ""
    public var areaDescription: String?
    public var colourHex: String = "#00FF00"
    public var opacity: Double = 0.3
    public var shapeType: String = "polygon"
    public var radiusMetres: Double?
    public var isVisible: Bool = true
    public var createdAt: Date = .now
    public var vertices: [AreaVertex] = []

    public init() {}

#if canImport(CloudKit)
    public static let recordType = "Area"
    public var recordID: CKRecord.ID = CKRecord.ID(recordName: UUID().uuidString)

    public init(record: CKRecord) {
        self.recordID = record.recordID
        self.id = record["id"] as? UUID ?? UUID()
        self.name = record["name"] as? String ?? ""
        self.areaDescription = record["areaDescription"] as? String
        self.colourHex = record["colourHex"] as? String ?? "#00FF00"
        self.opacity = record["opacity"] as? Double ?? 0.3
        self.shapeType = record["shapeType"] as? String ?? "polygon"
        self.radiusMetres = record["radiusMetres"] as? Double
        self.isVisible = record["isVisible"] as? Bool ?? true
        self.createdAt = record["createdAt"] as? Date ?? Date()
    }

    public func toRecord() -> CKRecord {
        let rec = CKRecord(recordType: Self.recordType, recordID: recordID)
        rec["id"] = id as CKRecordValue
        rec["name"] = name as CKRecordValue
        rec["areaDescription"] = areaDescription as CKRecordValue?
        rec["colourHex"] = colourHex as CKRecordValue
        rec["opacity"] = opacity as CKRecordValue
        rec["shapeType"] = shapeType as CKRecordValue
        rec["radiusMetres"] = radiusMetres as CKRecordValue?
        rec["isVisible"] = isVisible as CKRecordValue
        rec["createdAt"] = createdAt as CKRecordValue
        return rec
    }
#endif

    public static func == (lhs: Area, rhs: Area) -> Bool {
        lhs.id == rhs.id
    }
}

public final class AreaVertex: Codable, Equatable, Identifiable {
    public var id: UUID = .init()
    public weak var area: Area?
    public var latitude: Double = 0
    public var longitude: Double = 0
    public var index: Int32 = 0

    public init() {}

#if canImport(CloudKit)
    public static let recordType = "AreaVertex"
    public var recordID: CKRecord.ID = CKRecord.ID(recordName: UUID().uuidString)

    public init(record: CKRecord) {
        self.recordID = record.recordID
        self.id = record["id"] as? UUID ?? UUID()
        self.latitude = record["latitude"] as? Double ?? 0
        self.longitude = record["longitude"] as? Double ?? 0
        self.index = record["index"] as? Int32 ?? 0
    }

    public func toRecord() -> CKRecord {
        let rec = CKRecord(recordType: Self.recordType, recordID: recordID)
        rec["id"] = id as CKRecordValue
        rec["latitude"] = latitude as CKRecordValue
        rec["longitude"] = longitude as CKRecordValue
        rec["index"] = index as CKRecordValue
        return rec
    }
#endif

    public static func == (lhs: AreaVertex, rhs: AreaVertex) -> Bool {
        lhs.id == rhs.id
    }
}

